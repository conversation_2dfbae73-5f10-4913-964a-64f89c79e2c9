const {PAYMENT_STATUSES, ENTRY_STATUSES} = require("../../constants/teams");
const {HOUSING_STATUS_CODES} = require("../../constants/housing");

class EventTeamRegistrationService {
    constructor () {}

    async assignTeam(eventId, masterTeamId, masterClubId, divisionId, sendEmails = true) {
        const season = sails.config.sw_season.current;

        const [rosterTeamId, rosterClubId] = await Promise.all([
            this.#getTeamData(eventId, masterTeamId),
            this.#getClubData(eventId, masterClubId)
        ]);

        let team_id;
        if (rosterTeamId) {
            await this.#updateRosterData(rosterTeamId, masterClubId, rosterClubId, divisionId, eventId, sendEmails);

            team_id = rosterTeamId;
        } else {
            const {roster_team_id} = await this.#createRosterData(rosterClubId, masterTeamId, masterClubId, divisionId, eventId, sendEmails);

            team_id = roster_team_id;
        }

        await RosterSnapshotService
            .makeRosterSnapshot(masterTeamId, team_id, eventId, season)
            .catch(err => loggers.errors_log.error('Create Roster Members Error:', err));
    }

    async #getTeamData(eventId, masterTeamId) {
        const query = `
            SELECT rt.roster_team_id "id" 
            FROM "roster_team" rt 
            WHERE rt.event_id = $1 
                AND rt.master_team_id = $2`;

        const {rows: [team] = []} = await Db.query(query, [eventId, masterTeamId]);

        return team && team.id;
    }

    async #getClubData(eventId, masterClubId) {
        const query = `
            SELECT rc.roster_club_id "id"
            FROM "roster_club" rc
            WHERE rc.event_id = $1
                AND rc.master_club_id = $2`;

        const {rows: [club] = []} = await Db.query(query, [eventId, masterClubId]);

        return club && club.id;
    }

    async #createRosterData (rosterClubId, masterTeamId, masterClubId, divisionId, eventId, sendEmails = true) {
        let tr;
        try {
            tr = await Db.begin();

            const club = await this.#upsertRosterClubRow(tr, rosterClubId, masterClubId, eventId);

            const team = await this.#createRosterTeamRow(tr, masterTeamId, club, divisionId, eventId, sendEmails)

            if (!club.distance_to_event) {
                rosterClubId = rosterClubId || club.roster_club_id;

                await ClubService.geo.calculateDistanceToEvent(tr, rosterClubId, masterClubId, eventId);
            }

            await tr.commit();

            return team;
        } catch (err) {
            if (tr && !tr.isCommited) {
                await tr.rollback();
            }
            throw err;
        }
    }

    async #upsertRosterClubRow(tr, rosterClubId, masterClubId, eventId) {
        const query = `
            with update_club AS (
                update roster_club rc
                set deleted = (
                    select case when (count(rt.*) = 0) then now() else null end
                    from roster_team rt
                    where rt.roster_club_id = rc.roster_club_id
                        and rt.deleted is null
                )
                where roster_club_id = $1 and event_id = $2
                returning
                    'update'::text "action", club_name, code, is_local, distance_to_event,
                    roster_club_id, to_char(deleted, 'Mon DD, YYYY, HH12:MI AM') deleted
            ), insert_club AS (
                insert into roster_club (
                    club_name, code, address, city, state, zip,
                    country, region, master_club_id, event_id
                )
                select mc.club_name,
                    mc.code,
                    mc.address,
                    mc.city,
                    mc.state,
                    mc.zip,
                    mc.country,
                    mc.region,
                    mc.master_club_id,
                    $2
                from master_club mc
                where NOT EXISTS (SELECT * FROM update_club)
                    and mc.master_club_id = $3
                    and mc.code is not null
                returning
                    'insert'::text "action", club_name, code, is_local, distance_to_event,
                    roster_club_id, to_char(deleted, 'Mon DD, YYYY, HH12:MI AM') deleted
            )
            SELECT *
            FROM update_club
            UNION ALL
            SELECT *
            FROM insert_club`;

        const {rows: [club] = []} = await tr.query(
            query,
            [rosterClubId || -1, eventId, masterClubId]
        );

        if (!(club && club.roster_club_id)) {
            throw {validation: 'Club creation failed'}
        }

        return club;
    }

    async #createRosterTeamRow(tr, masterTeamId, club, divisionId, eventId, sendEmails = true) {
        // Create the team record
        const query = `
            insert into roster_team (team_name, organization_code, gender, age, rank, sport_id,
                master_team_id, roster_club_id, division_id, club_owner_id,
                date_entered, status_paid, status_entry, status_housing, event_id)
            select mt.team_name,
                mt.organization_code,
                mt.gender,
                mt.age,
                mt.rank,
                mt.sport_id,
                mt.master_team_id,
                $1,
                $2,
                mt.club_owner_id,
                now(),
                ${PAYMENT_STATUSES.NOT_PAID},
                ${ENTRY_STATUSES.PENDING},
                $3,
                $4
            from master_team mt
            where mt.master_team_id = $5
            returning team_name, roster_team_id, roster_club_id, division_id, status_paid, status_entry`;

        const {rows: [team] = []} = await tr.query(
            query,
            [
                club.roster_club_id, divisionId,
                club.is_local ? HOUSING_STATUS_CODES.VERIFIED : HOUSING_STATUS_CODES.NONE, eventId, masterTeamId
            ]
        );

        if (_.isEmpty(team)) {
            throw {validation: 'Team creation failed'}
        }

        // If sendEmails is false, mark the event_change entry to be skipped by the notification system
        if (!sendEmails) {
            await tr.query(`
                INSERT INTO event_change (event_id, action, roster_team_id, roster_club_id, sent_to_club)
                VALUES ($1, 'team.entered', $2, $3, NOW())
            `, [eventId, team.roster_team_id, team.roster_club_id]);
        }

        return team;
    }

    async #updateRosterData (rosterTeamId, masterClubId, rosterClubId, divisionId, eventId, sendEmails = true) {
        let tr;
        try {
            tr = await Db.begin();

            const team = await this.#updateRosterTeamRow(tr, rosterTeamId, divisionId);

            const {distance_to_event} = await this.#updateRosterClubRow(tr, rosterClubId, eventId);

            if (!distance_to_event) {
                await ClubService.geo.calculateDistanceToEvent(tr, rosterClubId, masterClubId, eventId);
            }

            // If sendEmails is false, mark the event_change entry to be skipped by the notification system
            if (!sendEmails) {
                await tr.query(`
                    INSERT INTO event_change (event_id, action, roster_team_id, roster_club_id, sent_to_club)
                    VALUES ($1, 'team.entered', $2, $3, NOW())
                `, [eventId, rosterTeamId, rosterClubId]);
            }

            await tr.commit();
        } catch(err) {
            if(tr && !tr.isCommited) {
                await tr.rollback();
            }
            throw err;
        }
    }

    async #updateRosterTeamRow(tr, rosterTeamId, divisionId) {
        const query = `
            UPDATE "roster_team"
            SET "deleted" = NULL, "division_id" = $2
            WHERE "roster_team_id" = $1
            RETURNING *`;

        const result = await tr.query(query, [rosterTeamId, divisionId]);
        return result.rows[0];
    }

    #updateRosterClubRow(tr, rosterClubId, eventId) {
        const query = `
            update roster_club rc 
            set deleted = ( 
                select
                    case
                        when (count(rt.*) = 0) then now()
                        else null
                    end 
                from roster_team rt 
                where rt.roster_club_id = rc.roster_club_id 
                    and rt.deleted is null 
            ) 
            where roster_club_id = $1 and event_id = $2 
            returning roster_club_id, distance_to_event, to_char(deleted, 'Mon DD, YYYY, HH12:MI AM') deleted`;

        return tr.query(query, [rosterClubId, eventId]);
    }
}

module.exports = EventTeamRegistrationService;
