angular.module('SportWrench')
.controller('Housing.Events.TeamsController', function (
    $scope, EntryStatusFactory, $stateParams, $http, $uibModal, $rootScope, APP_ROUTES, PaymentStatusFactory,
    HousingStatusFactory, teamHousingService
) {
    var event_id        = $stateParams.event;
    $scope.teams        = [];
    $scope.range        = {}; // from to
    $scope.limit        = 100;
    $scope.order_by     = {};
    $scope.filters      = {};
    $scope.current_page = 0;
    $scope.data         = {};
    $scope.states       = { events: APP_ROUTES.HOUSING_EVENTS };
    $scope.search = '';

    $scope.showReservationsColumns = function () {
        return $rootScope.isHousingManager() === 1
    }

    $scope.entry_statuses   = EntryStatusFactory.getItems();
    $scope.housing_statuses = HousingStatusFactory.getItems();
    $scope.payment_statuses = PaymentStatusFactory.getItems();
    
    function _load_teams (params, cb) {
        if(!$scope.data.event_name) params.event = true;
        $http.get('api/housing/events/' + event_id + '/teams', {
            params: params
        })
        .success(function(data, status) {
            $scope.total_teams = data.total_rows;
            if(!$scope.data.event_name) $scope.data.event_name = data.event_name;
            return cb(data, status)
        });
    }

    $scope._search = function() {
        _load_teams(getParams({ page: 0 }), function (data) {
            $scope.teams = data.teams;
            $scope.total_teams = data.total_rows;
            $scope.range.from = 1;
            $scope.current_page = 0;
            $scope.range.to = $scope.current_page * $scope.limit + (+data.teams.length);
        })
    };

    $scope.orderTeamsBy = function(column_name, asc) {
        $scope.order_by.column = column_name;       

        if (asc) $scope.order_by.asc = asc;
        else $scope.order_by.asc = !$scope.order_by.asc;

       _load_teams(getParams(), function (data) {
            $scope.teams = data.teams;
            $scope.range.from = 1;
            $scope.current_page = 0;
            $scope.range.to = $scope.current_page * $scope.limit + (+data.teams.length);
        });
    };

    $scope.toPrevPage = function() {
        if ($scope.current_page === 0) return;

        $scope.range.from = --$scope.current_page * $scope.limit + 1;

        _load_teams(getParams(), function (data) {
            $scope.teams = data.teams;
            $scope.range.to = $scope.current_page * $scope.limit + $scope.limit;
        });
    };

    $scope.toNextPage = function() {
        if ($scope.range.to >= $scope.total_teams) return;

        _load_teams(getParams({ page: ++$scope.current_page }), function (data) {
            $scope.range.from = $scope.current_page * $scope.limit + 1;
            $scope.teams = data.teams;            
            $scope.range.to = $scope.current_page * $scope.limit + (+data.teams.length);
        });
    };

    $scope.filterStatus = function (type, selection) {
        if(!selection.length && $scope.filters[type]) {
            delete $scope.filters[type];
        } else {
            $scope.filters[type] = selection.join(',');
        }

        __processFiltering();
    }

    $scope.showTeamInfo = function (team) {
        $uibModal.open({
            templateUrl: 'public/events/team-housing.html',
            controller: 'Housing.Team.Info.HousingController',
            controllerAs: 'ths',
            resolve: {
                team: function () {
                    return team;
                }
            }
        }).result.then(function (isTeamInfoChanged) {
            if (!isTeamInfoChanged) {
                return;
            }

            __processFiltering();
        });
    };

    $scope.orderTeamsBy('entered', false);

    $scope.getStatusHousingTooltip = function (team) {
        return $rootScope.isHousingManager() === 1 ? team.status_date : team.housing_status_changed_at;
    };

    $scope.exportTeamList = function() {
        teamHousingService.export(event_id);
    }

    function __processFiltering() {
        _load_teams(getParams(), function (data) {
            $scope.teams = data.teams;
            $scope.range.from = $scope.current_page * $scope.limit + 1;
            $scope.range.to = $scope.current_page * $scope.limit + (+data.teams.length);
        });
    }

    function getFilters() {
        const filters = {};

        for(let filter in  $scope.filters) {
            if($scope.filters[filter]) {
                if(filter === 'entry') {
                    filters.filter_entry = $scope.filters[filter];
                }

                if(filter === 'housing') {
                    filters.filter_housing = $scope.filters[filter];
                }

                if(filter === 'payment') {
                    filters.filter_payment = $scope.filters[filter];
                }
            }
        }

        return filters;
    }

    function getParams({
        limit = $scope.limit,
        page = $scope.current_page,
        order = [$scope.order_by.column, $scope.order_by.asc].join(','),
        search = $scope.search
    } = {}) {
        return Object.assign({}, { limit, page, order, search }, getFilters());
    }

})
