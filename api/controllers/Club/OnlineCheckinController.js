'use strict';

const argv 			= require('optimist').argv;
const Joi           = require('joi');

const { checkinResendSchema } = require('../../validation-schemas/online-checkin');
const { ENTRY_STATUSES } = require('../../constants/teams');

const QR_CONTENT_PREFIX 	= 'SWTm',
	  QR_CODE_PATH 			= 'images/qrcode/',
	  BASE_URL 				= sails.config.urls.home_page.baseUrl,
	  IMAGE_LINK			= `${BASE_URL}/${QR_CODE_PATH}`;

const CHAPERONE_ROLE_ID = 15;

module.exports = {
	// GET /api/club/event/:event/online-checkin/teams
	teamsList: function (req, res) {
		const 	$eventId 		= req.options.event || req.params.event,
				$masterClubId 	= Number(req.session.passport.user.master_club_id);

		if(!$masterClubId)
			return res.forbidden('No Club found');

		Db.query(
			`SELECT
			    rt.roster_team_id "team_id", rt.team_name, rt.organization_code "usav_code", d.is_qualifying, 
			    d.name "division_name", d.gender "division_gender",             
			    TO_CHAR (rt.online_checkin_date::TIMESTAMPTZ AT TIME ZONE e.timezone, 'MM/DD/YYYY HH12:MI AM') "online_checkin_date", 
                (
                	CASE
                        WHEN e.online_team_checkin_mode = 'primary_staff_barcodes' 
                        THEN 
                        (
                        	SELECT COALESCE(ARRAY_TO_JSON(ARRAY_AGG(ROW_TO_JSON("s"))), '[]'::JSON)
					        FROM (
					            SELECT 
					                FORMAT('%s %s', ms.first, ms.last) "name",
                                    etc.event_team_checkin_id::BOOLEAN "is_checked_in",
                                    ms.email
					            FROM "roster_staff_role" rsr
					            LEFT JOIN "event_team_checkin" etc
					            	ON etc.master_staff_id = rsr.master_staff_id 
					            		AND etc.roster_team_id = rt.roster_team_id
					            LEFT JOIN "master_staff_role" msr
				                    ON msr.master_team_id = rsr.master_team_id
										AND msr.master_staff_id = rsr.master_staff_id
					            INNER JOIN "master_staff" ms 
					                ON ms.master_staff_id = rsr.master_staff_id
										AND ms.deleted IS NULL
					            WHERE rsr.roster_team_id = rt.roster_team_id
                    				AND rsr.deleted_by_user IS NULL
                    				AND rt.deleted IS NULL
									AND COALESCE(rsr.primary, msr.primary) IS TRUE
									AND
					                    (CASE 
                                            WHEN (e.teams_settings->>'allow_chaperone_qr_code')::BOOLEAN IS NOT TRUE
                                                THEN COALESCE(NULLIF(rsr.role_id, 0), msr.role_id) <> $3
                                            ELSE TRUE
                                        END) 
					        ) "s"
                        )
                        ELSE 
                        (
	                        SELECT COALESCE(ARRAY_TO_JSON(ARRAY_AGG(ROW_TO_JSON("s"))), '[]'::JSON)
					        FROM (
					            SELECT 
					                FORMAT('%s %s', ms.first, ms.last) "name"
					            FROM "event_team_checkin" etc
					            INNER JOIN "master_staff" ms 
					                ON ms.master_staff_id = etc.master_staff_id
					            LEFT JOIN roster_staff_role rsr
					                ON rsr.master_staff_id = etc.master_staff_id
					            		AND rsr.roster_team_id = rt.roster_team_id     
					            LEFT JOIN "master_staff_role" msr
				                    ON msr.master_team_id = rsr.master_team_id
										AND msr.master_staff_id = rsr.master_staff_id  
					            WHERE etc.roster_team_id = rt.roster_team_id
					                AND etc.event_id = rt.event_id
					                AND
					                    (CASE 
                                            WHEN (e.teams_settings->>'allow_chaperone_qr_code')::BOOLEAN IS NOT TRUE
                                                THEN COALESCE(NULLIF(rsr.role_id, 0), msr.role_id) <> $3
                                            ELSE TRUE
                                        END)  
					        ) "s"
					    )
                    END
			    ) "staffers"
			FROM "roster_team" rt
			INNER JOIN "master_team" mt
			    ON mt.master_team_id = rt.master_team_id
			    AND mt.master_club_id = $2
            JOIN "division" d 
                ON d.division_id = rt.division_id 
                AND d.event_id = rt.event_id
			LEFT JOIN "event" e 
			    ON e.event_id = rt.event_id
			WHERE rt.event_id = $1
			    AND rt.status_entry = 12
			    AND rt.deleted IS NULL`,
			[$eventId, $masterClubId, CHAPERONE_ROLE_ID]
		).then(function (result) {
			res.status(200).json({
				teams: result.rows
			})
		}).catch(function (err) {
			res.customRespError(err);
		})
	},
	// GET /api/club/event/:event/online-checkin/staffers
	staffers: function (req, res) {
		const 	$eventId 		= req.options.event || req.params.event,
				$masterClubId 	= Number(req.session.passport.user.master_club_id),
                season = sails.config.sw_season.current;

		if(!$masterClubId)
			return res.forbidden('No Club found');

		OnlineCheckinService.team.getStaffers($eventId, $masterClubId, season).then(function (staffers) {
			res.status(200).json({
				staffers
			})
		}).catch(function (err) {
			res.customRespError(err);
		})
	},
	// POST /api/club/event/:event/online-checkin/apply
	applyCheckin: async function (req, res) {
		try {
			const 	$eventId 			= Number(req.params.event),
					$masterClubId 		= Number(req.session.passport.user.master_club_id),
					$staffers 			= req.body.staffers,
					$teamsList 			= _.filter(req.body.teams, function (val) {
						return _.isNumber(parseInt(val))
					}),
					season 				= sails.config.sw_season.current,
					$userID          = Number(req.session.passport.user.user_id);
	
			if (!$eventId) {
                throw { validation: 'Invalid Event Identifier' };
            }

			if(!$masterClubId) {
				return res.forbidden('No Club found');
			}

			if(!$teamsList.length) {
				return res.validation('No Teams Passed');
			}

			await OnlineCheckinService.team.checkin({
				eventID: $eventId,
				teamsList: $teamsList,
				masterClubID: $masterClubId,
				season,
				userID: $userID,
				staffers: $staffers,
			});

			res.ok();
		} catch(err) {
			res.customRespError(err);
		}
	},

    // GET /online-checkin/:hash
    showPrimaryStaffBarcodeDescription: function (req, res) {
        let hash = req.params.hash;

        OnlineCheckinService.barcodes.getOnlineCheckin(hash)
            .then(data => {
                res.render('club/barcode_description_primary_staff', data)
            }).catch(function (err) {
            if(err.validation) {
                res.render('500', { error: err.validation });
            } else {
                res.serverError();
            }
        })
    },

	// GET /online-checkin/:barcode/event/:event
    showBarcodeDescription: async function (req, res) {
        const $barcode = req.params.barcode,
            $eventId = req.options.event || req.params.event;

        if(!$barcode) {
            return res.serverError('Invalid barcode');
        }
        
        try {
            const mode = await getEventCheckinMode($eventId);
            
            if(mode === 'primary_staff_barcodes') {
                let data = await getCheckinData($barcode, $eventId);
                res.render('club/barcode_description_primary_staff', data);
            } else {
                const result = await Db.query(
                    `SELECT *
                    FROM (
                           SELECT
                             e.long_name "event_name",
                             (e."teams_settings"->>'chaperone_qr_code_border')::BOOLEAN AS "chaperone_qr_code_border",
                             e."teams_settings"->>'chaperone_qr_code_border_color' AS "chaperone_qr_code_border_color",
                             COALESCE(NULLIF(rsr.role_id, 0), msr.role_id) AS "staff_role_id",
                             FORMAT('%s %s', "ms".first, "ms".last) "staffer_name",
                             "mc".club_name,
                             ARRAY_TO_JSON(ARRAY_AGG(JSON_BUILD_OBJECT(
                                    'team_name', rt.team_name,
                                    'usav_code', rt.organization_code,
                                    'status_checkin', rt.status_checkin
                                    )
                                )
                             ) "teams"
                           FROM "event" e
                             INNER JOIN "master_staff" AS "ms"
                               ON ms.checkin_barcode = $2
                             INNER JOIN "master_club" AS "mc"
                               ON mc.master_club_id = ms.master_club_id
                             INNER JOIN "event_team_checkin" AS "etc"
                               ON "etc"."event_id" = "e"."event_id"
                               AND "etc"."master_staff_id" = "ms"."master_staff_id"
                             INNER JOIN "roster_team" AS "rt"
                               ON "rt"."roster_team_id" = "etc"."roster_team_id"
                               AND "rt"."status_entry" = $3
                               AND "rt"."deleted" IS NULL
                               AND "rt"."event_id" = "etc"."event_id"
                             LEFT JOIN "roster_staff_role" AS "rsr"
                               ON "rsr"."roster_team_id" = "rt"."roster_team_id"
                               AND "rsr"."master_staff_id" = "etc"."master_staff_id"
                               AND "rsr"."deleted_by_user" IS NULL
                               AND "rsr"."deleted" IS NULL
                             LEFT JOIN "master_staff_role" AS "msr"
                               ON "msr"."master_team_id" = "rsr"."master_team_id"
                               AND "msr"."master_staff_id" = "rsr"."master_staff_id"
                           WHERE e.event_id = $1
                           GROUP BY e.event_id, "rsr".role_id, "msr".role_id, "ms".first, "ms".last, "mc".club_name
                         ) data
                    WHERE data.teams IS NOT NULL`,
                    [$eventId, $barcode, ENTRY_STATUSES.ACCEPTED]
                );
                let data = _.first(result.rows);
                if(_.isEmpty(data)) {
                    throw { validation: 'This page is invalid or expired' };
                }
                
                data.all_checked_in_teams = _.every(data.teams, (team) =>{
                    return team.status_checkin === 'checkedin';
                });
                data.barcode_border_color = 'green';
                if(Number(data.staff_role_id) === OnlineCheckinService.team.CHAPERONE_ROLE_ID) {
                    if(data.chaperone_qr_code_border) {
                        data.barcode_border_color = data.chaperone_qr_code_border_color;
                    } else {
                        data.barcode_border_color = null;
                    }
                }
                data.imageLink = `${IMAGE_LINK}${QR_CONTENT_PREFIX}${$barcode}.png`;
                
                res.render('club/barcode_description', data);
            }
        } catch(err) {
            if(err.validation) {
                res.render('500', { error: err.validation });
            } else {
                res.serverError();
            }
        }
    },
    
	// GET /api/club/event/:event/online-checkin/validate/:team
	validateTeam: function (req, res) {
		const $eventId 	= req.params.event,
			  $teamId 	= Number(req.params.team);

		if(!$teamId)
			return res.validation('Invalid Team Identifier');

		CheckInRosterService.validateTeam($eventId, $teamId)
		.then(function (validationErrors) {
			res.status(200).json({
				errors: validationErrors
			})
		}).catch(function (err) {
			res.customRespError(err);
		})
	},

    // POST /online-checkin/:staffer/:team/resend
    resendPrimaryStaffEmail: function (req, res) {
        let masterStaffID = Number(req.params.staffer);
        let rosterTeamID  = Number(req.params.team);

        const { error } = checkinResendSchema.validate(req.body);

        if (error) {
            return res.validation({ validationErrors: error.details[0].message });
        }

        const { phone, email } = req.body;

        OnlineCheckinService.barcodes.resendPrimaryStaffEmail(rosterTeamID, masterStaffID, { email, phone })
            .then(() => {
                res.status(200).json({});
            })
            .catch(err => {
                res.customRespError(err);
            })
    }
}

async function getEventCheckinMode (eventID) {
    
    let query = squel.select().from('event', 'e')
        .field('e.online_team_checkin_mode', 'mode')
        .where('e.event_id = ?', eventID);
    
    const result = await Db.query(query, [eventID]);
    return (result.rows[0] && result.rows[0].mode) || null
    
}

async function getCheckinData (barcode, eventID) {
    let query =
        `SELECT
          e.long_name AS "event_name",
          FORMAT('%s %s', ms.first, ms.last) AS staff_name,
          e.website AS "event_website",
          rt.team_name,
          ms.checkin_barcode AS "barcode",
          e.name AS event_short_name,
          (e."teams_settings"->>'chaperone_qr_code_border')::BOOLEAN AS "chaperone_qr_code_border",
          e."teams_settings"->>'chaperone_qr_code_border_color' AS "chaperone_qr_code_border_color",
          rsr.role_id
        FROM event_team_checkin etc
        LEFT JOIN master_staff ms
          ON etc.master_staff_id = ms.master_staff_id
        LEFT JOIN roster_team rt
          ON etc.roster_team_id = rt.roster_team_id
        LEFT JOIN event e
          ON e.event_id = etc.event_id
        LEFT JOIN roster_staff_role rsr
          ON rsr.roster_team_id = rt.roster_team_id
          AND rsr.master_staff_id = ms.master_staff_id
        WHERE etc.event_id = $1 AND ms.checkin_barcode = $2`;
    
    const result = await Db.query(query, [eventID, barcode]);
    let data = result.rows[0] || null;
    
    if(!data) {
        throw { validation: 'No data found' }
    }
    
    data.barcode_border_color = 'green';
    if(Number(data.role_id) === OnlineCheckinService.team.CHAPERONE_ROLE_ID) {
        if(data.chaperone_qr_code_border) {
            data.barcode_border_color = data.chaperone_qr_code_border_color;
        } else {
            data.barcode_border_color = null;
        }
    }
    
    data.imageLink = `${IMAGE_LINK}${QR_CONTENT_PREFIX}${barcode}.png`;
    return data;
}
