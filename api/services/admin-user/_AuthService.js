const passport = require('passport');

class AuthService {
    async signIn (req, res) {
        try {
            let user = await this.__auth(req, res);

            if (!user.activated) {
                throw {
                    validationErrors: {
                        message : 'Your account is not activated',
                        path    : 'email',
                    },
                    code: 400,
                };
            }

            await this.__login(user, req);

            return {
                first   : user.first,
                last    : user.last,
                email   : user.email,
            }
        } catch (err) {
            throw err;
        }
    }

    signOut (req, res) {
        res.clearCookie('remember_me');
        res.clearCookie('sails.sid');

        if (req.user && req.user.user_id && req.sessionID) {
            RedisService.delUserDataMonitorKey(req.user.user_id, req.sessionID)
                .catch(ErrorSender.defaultError.bind(ErrorSender));
        }

        return new Promise((resolve, reject) => {
            req.logout(function (err) {
                if (err) {
                    loggers.errors_log.error(err);
                }
                req.session.destroy(function (err) {
                    if (err) {
                        loggers.errors_log.error(err);
                    }
                    resolve();
                });
            });
        })

    }

    __auth (req, res) {
        return new Promise((resolve, reject) => {
            passport.authenticate('admin-local', (err, user, info) => {
                if (err) {
                    reject(err);
                } else if (_.isEmpty(user)) {
                    reject({ validationErrors: [info], code: 400 });
                } else {
                    RedisService.setUserDataMonitorKey(user.user_id, req.sessionID)
                        .catch(ErrorSender.defaultError.bind(ErrorSender));
                    resolve(user);
                }
            })(req, res);
        });
    }

    __login (user, req) {
        return new Promise((resolve, reject) => {
            req.logIn(user, (err) => {
                if (err) {
                    reject(err);
                } else {
                    resolve();
                }
            })
        })
    }
}

module.exports = new AuthService();
