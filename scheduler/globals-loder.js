'use strict';

const path = require('path');
const utils = require('../api/lib/swUtils');
const fs = require('fs');

/* jshint node:true */
const includeAll      = require('include-all');

const CONFIG_FOLDER = path.resolve(__dirname, '..', 'config');
const HOOKS_FOLDER = path.resolve(__dirname, '..', 'api', 'hooks');
const SERVICES_FOLDER = path.resolve(__dirname, '..', 'api', 'services');

const ENV_CONFIG      = process.env.NODE_ENV ? require(CONFIG_FOLDER + '/env/'+process.env.NODE_ENV) : null;

const log                   = ENV_CONFIG && ENV_CONFIG.log || require(CONFIG_FOLDER + '/log').log;
const envConnection         = ENV_CONFIG;
const {db}                  = require(CONFIG_FOLDER + '/db');
const s3Config              = ENV_CONFIG && ENV_CONFIG.s3 || require(CONFIG_FOLDER + '/s3').s3;
const stripeConfig          = require(CONFIG_FOLDER + '/stripe');
const swtConfig             = require(CONFIG_FOLDER + '/swt');
const plivoConfig           = require(CONFIG_FOLDER + '/plivo').plivo;
const {sw_season}           = require(CONFIG_FOLDER + '/sw_season');
const twilioConfig          = ENV_CONFIG && ENV_CONFIG.twilio || require(CONFIG_FOLDER + '/twilio').twilio;
const eventSocialLinks      = require(CONFIG_FOLDER + '/event_social_links').eventSocialLinks;
const urls                  = ENV_CONFIG && ENV_CONFIG.urls || require(CONFIG_FOLDER + '/urls').urls;
const webpointConfig        = require(CONFIG_FOLDER + '/webpoint').webpoint;
const pmx_monitoring        = ENV_CONFIG && ENV_CONFIG.pmx_monitoring || require(CONFIG_FOLDER + '/pmx_monitoring').pmx_monitoring;
const applePass             = require(CONFIG_FOLDER + '/applePass').applePass;
const redisQueue            = require(CONFIG_FOLDER + '/redis_queue').redis_queue;
const sportsEngine          = ENV_CONFIG && ENV_CONFIG.sportsEngine || require(CONFIG_FOLDER + '/sportsEngine').sportsEngine;
const tilled                = ENV_CONFIG?.tilled || require(CONFIG_FOLDER + '/tilled').tilled;
const aau                   = ENV_CONFIG && ENV_CONFIG.aau || require(CONFIG_FOLDER + '/aau').aau;
const salesHub              = ENV_CONFIG && ENV_CONFIG.salesHub || require(CONFIG_FOLDER + '/salesHub').salesHub;
const ballerTv              = ENV_CONFIG && ENV_CONFIG.ballerTv || require(CONFIG_FOLDER + '/ballerTv').ballerTv;
const verticalInsurance     = ENV_CONFIG && ENV_CONFIG.verticalInsurance || require(CONFIG_FOLDER + '/verticalInsurance').verticalInsurance;
const swtApp                = ENV_CONFIG && ENV_CONFIG.swtApp || require(CONFIG_FOLDER + '/swt_app').swtApp;
const justifi               = ENV_CONFIG && ENV_CONFIG.justifi || require(CONFIG_FOLDER + '/justifi').justifi;

    let localConnection;
try {
    localConnection = require(CONFIG_FOLDER + '/local');
} catch (e) {
    localConnection = {};
}


function init() {
    global._     = require('lodash');

    let config   = __createSailsConfig();
    global.sails = { config };

    global.knex = require('knex')({client: 'pg'});

    sails.hooks = __loadHooks();

    //TODO: implement hook.defaults

    Object.keys(sails.hooks).forEach((name) => {
        const hook = sails.hooks[name];
        if(_.isFunction(hook.configure)) {
            hook.configure();
        }
    });

    const hooksInitialization = Object.keys(sails.hooks).forEach((name) => {
        const hook = sails.hooks[name];
        return new Promise((resolve, reject) => {
            if(_.isFunction(hook.initialize)) {
                hook.initialize(() => {
                    resolve(name);
                });
            }
            else {
                resolve(name);
            }
        })
            .then((name) => {
                //sails.emit(`hook:${name}:loaded`);
                return name;
            })
    });

    let services = __includeServices();

    _.each(services, (service, identity) => {
        global[identity] = service;
    });

    //TODO: await Promise.all(hooksInitialization)
}

function __createSailsConfig() {
    return {
        connections : __getDbConnection('connections'),
        log         : log,
        db          : db,
        stripe_api  : stripeConfig,
        s3          : s3Config,
        swt         : swtConfig.swt,
        plivo       : plivoConfig,
        sw_season   : sw_season,
        twilio      : twilioConfig,
        sw_domain   : urls.home_page.baseUrl,
        urls        : urls,
        webpoint    : webpointConfig,
        redis_queue : redisQueue,
        tilled      : tilled,
        eventSocialLinks,
        pmx_monitoring,
        applePass,
        sportsEngine,
        aau,
        salesHub,
        verticalInsurance,
        ballerTv,
        swtApp,
        justifi,
    };
}

function __loadHooks() {
    return fs.readdirSync(HOOKS_FOLDER).reduce((hooks, name) => {
        let hookPath = path.resolve(HOOKS_FOLDER, name);
        if(fs.lstatSync(hookPath).isFile()) {
            name = name.replace(/\.[^/.]+$/, "");
        }
        hooks[name] = require(hookPath)(sails);
        return hooks;
    }, {});
}

function __includeServices() {
    return includeAll({
        dirname         : (SERVICES_FOLDER),
        filter          : /^([^.]+)\.(?:(?!md|txt).)+$/,
        depth           : 1,
        caseSensitive   : true
    });
}

function __getDbConnection(type) {
    return localConnection[type] ? localConnection[type] : envConnection[type];
}

module.exports = { init };
