angular.module('SportWrench')

.factory('staffService', function($http) {
    var _getStaff = function() {
        return $http.get('/master_staff');
    };
    var _getStaffInClub = function() {
        return $http.get('/api/master_staff?showStaffInClub=true')
    };
    var _createStaff = function(staffObj) {
        return $http.post('/api/master_staff', staffObj);
    };
    var _getStaffById = function(id) {
        return $http.get('/api/master_staff/' + id) 
    };
    var _updateStaff = function(id, staff) {
        return $http.put('/api/master_staff/' + id, staff)
    };
    var _getStaffByRoles = function(role, master_team_id) {
        return $http.get('/api/master_staff?role=' + role + '&team=' + master_team_id)
    }

    var _getClubStaff = function() {
        return $http.get('/api/master_staff');
    };
    var _getClubStaffById = function(id) {
        return $http.get('/api/master_staff/' + id);
    };

    var _updateStaffRole = function(master_staff_id, team_role) {
        return $http.put('/api/master_staff/' + master_staff_id + '/role/team/' + team_role.id, {
            role_id: team_role.role_id || 0
        });
    };

    var _getTeamStaff = function(id) {
        return $http.get('/api/master_staff/team/' + id)
    };

    return {
        getStaffByRoles: function(role, master_team_id,callBack) {
            _getStaffByRoles(role, master_team_id).then(function(resp) {
                callBack(resp);
            })
        },
        getStaffInclub: function(callBack) {
            _getStaffInClub().then(function(resp) {
                callBack(resp)
            })
        },
        getStaff: function(callBack) {
            _getStaff().then(function(resp) {
                callBack(resp);
            })
        },
        createStaff: function(obj, callBack) {
            _createStaff(obj).then(function(resp) {
                callBack(resp);
            })
        },
        getStaffById:function(id, callBack) {
            _getStaffById(id). then(function(resp) {
                callBack(resp);
            })
        },
        updateStaff: function(id, staff, cbSuccess, cbError) {
            _updateStaff(id, staff).then(function(resp) {
                if(cbSuccess) cbSuccess(resp);
            }, function(resp) {
                if(cbError) cbError(resp);
            });
        },
        getClubStaff: function(callBack) {
            _getClubStaff().then(function(resp) {
                callBack(resp);
            })
        },
        getClubStaffById: function(id, callBack) {
            _getClubStaffById(id).then(function(resp) {
                callBack(resp);
            })
        },
        addStaffToTeam: function(id, staff) {
            return $http.post('/api/master_staff/move', {
                team: id,
                staff: staff
            })
        },
        removeStaffFromTeam: function (staff) {
            return $http.post('/api/master_staff/remove', {
                staff: staff
            })
        },
        changeRoleRows: function (staffId, teamId, data, action) {
            return $http.post('/api/master_staff/' + staffId + '/team/' + teamId + '/role/' + action, data);
        },
        removePrimaryTeam: function(master_staff_id, team_id) {
            return $http.post('/api/master_staff/' + master_staff_id + '/role/team/' + team_id + '/primary/remove')
        },
        updateStaffRole: function(master_staff_id, team_role, cbSuccess, cbError) {
            _updateStaffRole(master_staff_id, team_role).success(function(resp) {
                cbSuccess(resp);
            }).error(function(resp) {
                cbError(resp);
            });
        },
        getTeamStaff: function(id, callBack) {
            _getTeamStaff(id).success(function(resp) {
                callBack(resp);
            });
        }, 
        roster: {
            getAssignedEvents: function (staff) {
                return $http.post('/api/v2/club/staff/events_list', {
                    staff: staff
                })
            }
        }
    }
})
