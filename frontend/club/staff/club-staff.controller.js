angular.module('SportWrench')

.controller('ClubStaffController', ClubStaffController);

function ClubStaffController (
    $rootScope, $scope, staffService, $state, $uibModal, $filter, $interval, ClubTeamsService,
    INTERNAL_ERROR_MSG, toastr, APP_ROUTES, _, ClubWebpointService, STAFF_MEMBER_TYPE, SANCTIONING_BODY,
    masterClubService, GENDER_VALUES
) {
    $scope.staff = [];
    $scope.queue_data = {};
    $scope.checkedWebpoint = {};
    $scope.utils = {
        webpointLoading: {}
    };

    $scope.STAFF_MEMBER_TYPE = STAFF_MEMBER_TYPE;
    $scope.SANCTIONING_BODY = SANCTIONING_BODY;
    $scope.GENDER_VALUES = GENDER_VALUES;

    var loadingIntervalInstance = null;
    
    $scope.editStaff = function(id) {    
        $state.go(APP_ROUTES.CD.STAFF_UPDATE, {
            master_staff_id: id
        });
    }

    $scope.updateWebpoint = function (id) {
        
        $scope.utils.webpointLoading[id] = true;
        $scope.checkedWebpoint[id]       = true;

        ClubWebpointService.syncStaffer(id)
        .then(data => {

            for (let staffer of $scope.opts.filtered_staff) {
                if (staffer.master_staff_id === id) {

                    staffer.safesport_statusid  = data.safesport_status;
                    staffer.bkg                 = data.bg_status;
                    staffer.cert                = data.cert;

                    break;
                }
            }

            $scope.utils.webpointLoading[id] = false;

            toastr.success('Checked');
        })
        .catch(() => {
            $scope.utils.webpointLoading[id] = false;
        });
    };

    $scope.opts = {
        filtered_staff: undefined,
        selected_count: 0,
        certsSelection: {},
        selection: {},
        all_selected: false
    };

    $scope.order = {       
        reverse: false,
        column: 'last',
        orderData: function(colName) {
            this.column = colName;
            this.reverse = !this.reverse;
        }
    };

    $scope.filters = {
        teams: {},
        assigned: undefined, 
        cert: [],
        sanctionedBody: {},
    };

    $scope.findEventsAssignedTo = function (teamId) {
        var selectedStaff = _.map(_getSelectedItems(this.opts.selection), function (v) {
            return parseInt(v, 10);
        });
        if(!selectedStaff.length) return;
        

        ( (teamId)
            ?staffService.addStaffToTeam(teamId, selectedStaff)
            :staffService.removeStaffFromTeam(selectedStaff)
        )
        .then(function () {
            _load_staff(function() {
                $scope.opts.all_selected = false;
                $scope.check_all();
                toastr.success('Successful Operation!')
            });
        }, function (resp) {
            if(resp && resp.data && resp.data.validation) {
                toastr.error(resp.data.validation)
            }
        })
    }

    $scope.check_all = function () {
        if(this.opts.all_selected) {
            for(var i = 0, l = this.staff.length; i < l; ++i) {
                this.opts.selection[this.staff[i].master_staff_id] = true;
            }
            this.opts.selected_count = this.staff.length;
        } else {
            this.opts.selection = {};
            this.opts.selected_count = 0;
        }
    }

    $scope.staffCheckedChanged = function (id) {
        if(this.opts.selection[id])
            this.opts.selected_count++;
        else
            this.opts.selected_count--;
    }

    function _load_staff (done) {
        $scope.loading = true;
        loadingIntervalInstance = $interval(function getData () {  
            staffService.getClubStaff(function (resp) {
                if(resp.data.staff) {
                    $scope.loading = false;
                    $scope.staff = resp.data.staff;
                    $scope.filters.cert = resp.data.certs
                    $interval.cancel(loadingIntervalInstance);
                    $scope.queue_data.requested = undefined;
                    if(done) done(resp);
                } else if(resp.data.queue_data) {
                    $scope.queue_data = resp.data.queue_data;                                                  
                }     
            });
            return getData;
        }(), 1000 * 10);
    }

    _load_staff();

    var _genderFilter = function (staff) {
        return (!$scope.filters.gender 
                    || ($scope.filters.gender === 'both') 
                    || (staff.gender === GENDER_VALUES.NON_BINARY)
                    || (staff.gender === $scope.filters.gender) )
                    
    }

    var _teamFilter = function (staff, teams) {
        if($scope.filters.assigned === true)
            return (staff.teams.length > 0)
        else if($scope.filters.assigned === false)
            return staff.teams.length === 0

        if(!teams.length) return true
        for(var i = 0, l = staff.teams.length, id; i < l; ++i) {
            id = staff.teams[i].master_team_id;
            if(id && teams.indexOf(id.toString()) >= 0)
                return true;
        } 
        return false;
    }       

    var _certFilter = function (staff, certs) {
        if(!certs.length) return true;
        return (certs.indexOf(('' + staff.cert)) >= 0)
    }

    var _searchFilter = function (staff) {
        if(!$scope.filters.search) return true;
        var search = $scope.filters.search.toLowerCase().trim();

        if( ( staff.organization_code && staff.organization_code.toLowerCase().indexOf(search) >= 0 ) || 
            ( staff.team_code && staff.team_code.toLowerCase().indexOf(search) >= 0 ) || 
            ( staff.first && staff.first.toLowerCase().indexOf(search) >= 0 ) || 
            ( staff.last && staff.last.toLowerCase().indexOf(search) >= 0 ) || 
            ( staff.email && staff.email.toLowerCase().indexOf(search) >= 0 ))
                return true;
            return false;
    }

    var filters = function (staff, selectedTeams, selectedCerts) {
        return _genderFilter(staff) && _teamFilter(staff, selectedTeams) && _certFilter(staff, selectedCerts)
            && _searchFilter(staff) && masterClubService.sanctionedBodyFilter(staff, $scope.filters.sanctionedBody);
    }

    $scope.$watch('[staff, filters]', function (newVal) {
        if (newVal[0] && newVal[0].length) {
            var selectedTeams = _getSelectedItems($scope.filters.teams),
                selectedCerts = _getSelectedItems($scope.filters.certsSelection);
            $scope.opts.filtered_staff = $filter('filter')(newVal[0], function (value) {
                return filters(value, selectedTeams, selectedCerts)
            });
        }
    }, true);

    function _getSelectedItems(obj) {
        return _.keys(
                    _.pick(obj, function (v) {
                        return v === true
                    })
                );
    }

    $scope.openTeamModal = function (t) {
        if(_.isEmpty(t)) return;
        $uibModal.open({
            templateUrl: 'club/teams/edit-team-roster.html',
            controller: 'EditTeamRosterController',
            resolve: {
                club: function () {
                    return $scope.club;
                },
                team: function () {
                    return t.master_team_id
                }
            }
        }).result.then(function () {
        }, function () {
        })
    }

    $scope.openMinorTeamsModal = function (t, first, last) {
        if (_.isEmpty(t)) return;
        $uibModal.open({
            templateUrl: 'club/staff/minor-teams/minor-teams.html',
            controller: 'MinorTeamsController',
            resolve: {
                teams: function () {
                    return t
                },
                first: function() {
                    return first
                },
                last: function() {
                    return last
                }
            }
        }).result.then(function () {
        }, function () {
        })
    }

    $scope.$on('club.staff.reloadList', function () {
        _load_staff();
    })

    $scope.$on('$destroy', function() {
        if(loadingIntervalInstance) $interval.cancel(loadingIntervalInstance)
    });

    $scope.getCertLabel = function(cert) {
        return cert || 'No Cert';
    }

    $scope.reloadList = function () {
        _load_staff();
    }

    $scope.clubHasUsavAndAauSanctioning = masterClubService.clubHasUsavAndAauSanctioning($scope.club);
}
