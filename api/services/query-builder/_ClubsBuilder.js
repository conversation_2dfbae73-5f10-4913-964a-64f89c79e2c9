'use strict';

const swUtils = require('../../lib/swUtils');
const { USAV_SEASONALITY } = require('../../constants/teams');

const ORDER_ALIASES = {
    'name'      : 'rt.team_name',
    'code'      : 'rt.organization_code',
    'division'  : 'd.short_name',
    'club'      : 'rc.club_name',
    'paid'      : 'rt.date_paid',
    'created'   : 'p.created',
    'housing'   : '(CASE WHEN rc.is_local = TRUE THEN rt.date_paid ELSE rt.date_housing END)',
    'completed' : 'rt.date_completed',
    'entered': 'rt.date_entered',
};

class ClubsQueryBuilder {
    constructor (commonQueryParts) {
        this.commonQueryParts = commonQueryParts;
    }

	get CLUB_DIRECTOR_RECEIVER_TYPE () {
	    return 'cd';
    }

    get STAFF_RECEIVER_TYPE () {
	    return 'staff';
    }

    get TEST_RECEIVER() {
        return SQLQueryBuilderUtils.markTestData({
            event_name      : 'Test Event Name',
            event_city      : 'Atlanta',
            event_website   : 'www.test-event.com',
            event_email     : '<EMAIL>',
            event_month     : 'June',
            club_name       : 'Test Club',
            receiver_first  : 'John',
            receiver_last   : 'Smith',
            receiver_name   : 'John Smith',
            director_first  : 'John',
            director_last   : '<PERSON>',
            director_name   : 'John Smith',
            roster_teams    : 'Team One, Team Two, Team Three',
            teams_divs      : 'Team One (12 Club), Team Two (21 Club), Team Three (12 Club)',
            event_short_name: 'event_short_name',
            roster_teams_ul : 'roster_teams_ul',
            qr_code_image   : 'qr_code_image',
            description_link: 'description_link',
            social_icons    : 'social_icons',
            facebook_icon   : 'facebook_icon',
            twitter_icon    : 'twitter_icon',
            instagram_icon  : 'instagram_icon',
            snapchat_icon   : 'snapchat_icon'
        })
    }

	list (query, params, fields, method, options = {}) {
	    const { hideOrderBy } = options;

		let _query           = query || squel.select(),

        $limit                  = +params.limit,
        $page                   = +params.page,
        $order                  = params.order,
        $revert                 = params.revert,
        $division               = params.division,
        $search                 = params.search,
        $entry                  = params.entry,
        $payment                = params.payment,
        $housing                = params.housing,
        $members                = params.members,
        $teams                  = params.teams,
        $event_id               = +params.event,
        $duplicateSingleQuote   = params.duplicateSingleQuote || false;

	    _query.left_join('roster_club', 'rc', 'rc.roster_club_id = rt.roster_club_id')
	    _query.left_join('division', 'd', 'd.division_id = rt.division_id');
	    _query.left_join('event', 'e', 'e.event_id = rt.event_id');
	    _query.left_join('master_club', 'mc', 'mc.master_club_id = rc.master_club_id')

   		_query.where('rt.event_id = ?', $event_id);
   		_query.where('rt.deleted IS NULL');

      if($housing && $housing.indexOf('localDistance') >= 0) {
          _query.where('rc.distance_to_event <= e.housing_local_teams_distance');
          _query.where('rc.is_local IS NOT TRUE');
          $housing = _.without($housing, 'localDistance');
      }

      if ($housing && $housing.indexOf('local') >= 0) {
          _query.where('rc.is_local = true');
          $housing = _.without($housing, 'local');
      }

      if($housing && $housing.includes('loyalty')) {
          _query.where('rt.ths_loyalty = 1');
          $housing = _.without($housing, 'loyalty');
      }
      
      if ($housing && $housing.includes('withoutDistance')) {
          _query.where('rc.distance_to_event IS NULL');
          $housing = _.without($housing, 'withoutDistance');
      }

    	if(Array.isArray($teams) && $teams.length) {
    	    _query.where(`rt.roster_team_id IN (${swUtils.numArrayToString($teams)})`);
    	} else {

	        if(Array.isArray($division) && $division.length) {
	            _query.where(`rt.division_id IN (${swUtils.numArrayToString($division)})`);
	        }

	        if($search) {
	            let formattedSearch = '%' + swUtils.escapeStr($search, { duplicateSingleQuote: $duplicateSingleQuote }) + '%';

	            if($search.length === 2 && method !== 'doubles') {
	                _query.where('rc.region::text ILIKE ?', formattedSearch);
	            } else {
	                let expr = squel.expr()
	                    .and('rt.team_name::text ILIKE ?', formattedSearch)
	                    .or('rt.organization_code::text ILIKE ?', formattedSearch)
	                    .or('d.name::text ILIKE ?', formattedSearch);

	                if(method !== 'doubles') {
	                    expr.or('rc.club_name::text ILIKE ?', formattedSearch);
	                }
                    
	                _query.where(expr);
	            }
	        }

	        if(Array.isArray($entry) && $entry.length) {
	            _query.where(`rt.status_entry IN (${swUtils.numArrayToString($entry)})`);
	        }	

	        if(Array.isArray($payment) && $payment.length) {
	            _query.where(`rt.status_paid IN (${swUtils.numArrayToString($payment)})`);
	        }	

	        if(Array.isArray($housing) && $housing.length) {
	            _query.where(`rt.status_housing IN (${swUtils.numArrayToString($housing)})`);
	        }

          if ($members && $members.length) {
              if ($members.indexOf('no_athletes') >= 0) {
                  _query.where('rt.roster_athletes_count = 0');
              }
              if ($members.indexOf('no_staff') >= 0) {
                  _query.where('rt.roster_staff_count = 0');
              }
              if ($members.indexOf('invalid_roster') >= 0) {
                  _query.where(`rt.is_valid_roster IS FALSE
                    OR rt.roster_athletes_count < COALESCE(NULLIF(e.mincount_enter, 0), NULLIF(e.mincount_accept, 0), 7)`);
              }
              if ($members.indexOf('not_updated_clubs') >= 0) {
                  _query.where('mc.profile_completed_at IS NULL')
              }

              /**
               *  'locked' and 'not_locked' are not mutually exclusive. If they both will be in query, query will return
               *  empty data. So we need skip this step if filter '$members' contains each of them.
               */
              if ($members.indexOf('locked') === -1 || $members.indexOf('not_locked') === -1) {
                  if ($members.indexOf('locked') >= 0) {
                      _query.where(`rt.locked IS TRUE`);
                  }
                  if ($members.indexOf('not_locked') >= 0) {
                      _query.where(`rt.locked IS NOT TRUE`);
                  }
              }

              if($members.includes('bid_accepted') || $members.includes('bid_not_accepted')) {
                  if ($members.includes('bid_accepted')) {
                      _query.where(`CAST(rt.extra ->> 'bid_agreement_accepted' AS BOOLEAN) IS TRUE`);
                  }
                  if ($members.includes('bid_not_accepted')) {
                      _query.where(`CAST(rt.extra ->> 'bid_agreement_accepted' AS BOOLEAN) IS FALSE`);
                  }
              }

              if($members.includes('seasonality_local') || $members.includes('seasonality_full')) {
                  let local = $members.includes('seasonality_local');
                  let full = $members.includes('seasonality_full');

                  if(local && !full) {
                      _query.where(`rt.seasonality = ?`, USAV_SEASONALITY.LOCAL);
                  }
                  if(!local && full) {
                      _query.where(`rt.seasonality = ?`, USAV_SEASONALITY.FULL);
                  }
                  if(local && full) {
                      _query.where(
                          `rt.seasonality = ? OR rt.seasonality = ?`,
                          USAV_SEASONALITY.FULL, USAV_SEASONALITY.LOCAL
                      );
                  }
              }

              /**
               *  'online_checkin' and 'no_online_checkin' are not mutually exclusive. If they both will be in query, query will return
               *  empty data. So we need skip this step if filter '$members' contains each of them.
               */
              if ($members.indexOf('online_checkin') === -1 || $members.indexOf('no_online_checkin') === -1) {
                  if ($members.indexOf('online_checkin') >= 0) {
                      _query.where(`rt.online_checkin_date IS NOT NULL`);
                  }
                  if ($members.indexOf('no_online_checkin') >= 0) {
                      _query.where(`rt.online_checkin_date IS NULL`);
                  }
              }

              /**
               *  'checkedin' and 'not_checkedin' are not mutually exclusive. If they both will be in query, query will return
               *  empty data. So we need skip this step if filter '$members' contains each of them.
               */
              if ($members.indexOf('checkedin') === -1 || $members.indexOf('not_checkedin') === -1) {
                  if ($members.indexOf('checkedin') >= 0) {
                      _query.where(`rt.status_checkin = 'checkedin'`);
                  }
                  if ($members.indexOf('not_checkedin') >= 0) {
                      _query.where(`rt.status_checkin != 'checkedin' OR rt.status_checkin IS NULL`);
                  }
              }
              if($members.includes('lack_of_athletes')) {
                  _query.where(
                      `rt.roster_athletes_count < COALESCE(NULLIF(e.mincount_enter, 0), NULLIF(e.mincount_accept, 0), 7)`
                  );
              }
          }

	        if($limit) {
	            _query.limit($limit);
	        }

	        if($page) {
	            let offset = (!!$limit)?(($page - 1) * $limit):($page - 1);
	            _query.offset(offset);
	        }	

	        if (!hideOrderBy) {
                if(ORDER_ALIASES[$order]) {
                    _query.order(ORDER_ALIASES[$order], ($revert === 'true'));
                    _query.order('rt.organization_code');
                    _query.order('rt.roster_team_id');
                } else if ($order !== null) {
                    _query.order('rt.date_entered', false);
                    _query.order('rt.organization_code');
                    _query.order('rt.roster_team_id');
                }
            }
	    }

	    if(fields && fields.length) {
	        _.each(fields, function (el) {
	            _query.field(el.column, el.alias);
	        })
	    }

	    return _query;
	}

	__newsletterEmailReceiversList (eventID, receiversData) {
	    let receiversType;

	    if(receiversData.receivers_type) {
            receiversType = receiversData.receivers_type;
	        delete receiversData.receivers_type;
        }

        const { templateID, emailSendingID, emailCategory } = receiversData.emailParams;

		let sqlParams = _.defaults(receiversData, { event: eventID, order: null });
         
        let query;

        if (receiversData.eventRegMethod === 'doubles') {
            let subquery = 
                squel.select().from('roster_athlete', 'ra')
                    .field(`STRING_AGG(FORMAT('"%s %s" <%s>', ma.first, ma.last, ma.email), ', ')`)
                    .left_join('master_athlete', 'ma', 'ma.master_athlete_id = ra.master_athlete_id')
                    .where('ra.roster_team_id = rt.roster_team_id')
                    .where('ma.first IS NOT NULL')
                    .where('ma.last IS NOT NULL')
                    .where('ma.email IS NOT NULL');

          	query = this.list(squel.select().from('roster_team', 'rt'), sqlParams, [
                { column: subquery,                 alias: 'to'             },
                { column: 'rt.team_name',           alias: 'team'           },
                { column: 'rt.status_entry::INT',   alias: 'status_entry'   }
            ]);
        } else {
            let coreColumns = [
                {
                    column: `STRING_AGG(rt.team_name, ', ' ORDER BY rt.organization_code, rt.team_name)`,
                    alias : 'roster_teams'
                }, {
                    column: `ARRAY_AGG(rt.roster_team_id ORDER BY rt.organization_code, rt.team_name)`,
                    alias : 'team_ids'
                }, {
                    column:
                        `STRING_AGG(
                      FORMAT('%s (#d:%s#%s)', rt.team_name, d.gender, d.short_name), 
                      ', ' ORDER BY rt.organization_code, rt.team_name
                     )`,
                    alias :
                        'teams_divs'
                },
                { column: 'rc.club_name'                              },
                { column: 'e.long_name'     , alias: 'event_name'     },
                { column: 'e.city'          , alias: 'event_city'     },
                { column: 'e.email'         , alias: 'event_email'    },
                { column: 'e.website'       , alias: 'event_website'  },
                {
                    column:
                        `TO_CHAR(TO_TIMESTAMP(EXTRACT(MONTH FROM e.date_start)::TEXT, 'MM'), 'Month')`,
                    alias: 'event_month'
                }
            ];

            let existsQuery;

            if (templateID > 0) {
                existsQuery = squel.select().from('event_email', 'ee')
                    .field('ee.event_email_id')
                    .where('ee.event_i' +
                        'd = e.event_id')
                    .where('ee.email_template_id = ?', templateID);
            }

            let clubDirectorQuery = this.__clubDirectorReceiverQuery__(
                coreColumns, sqlParams, existsQuery, emailSendingID, emailCategory
            );

            let staffQuery        = this.__staffersReceiverQuery__(
                coreColumns, sqlParams, existsQuery, emailSendingID, emailCategory
            );

            if(!receiversType) {
                query = clubDirectorQuery.union(staffQuery);
            } else if(receiversType === this.CLUB_DIRECTOR_RECEIVER_TYPE) {
                query = clubDirectorQuery;
            } else if(receiversType === this.STAFF_RECEIVER_TYPE) {
                query = staffQuery;
            }
        }

        return query;
	}

    __clubDirectorReceiverQuery__ (coreColumns, sqlParams, existsQuery, emailSendingID, emailCategory) {
        let columns = [
            {
                column: `concat_ws(
                            '${EmailService.MULTIPLE__NOT_FORMATTED_RECEIVERS_STRING}',
                            mc.director_email, 
                            mc.administrative_email
                        )`,
                alias:  'email'
            },
            { column: 'INITCAP(mc.director_first)'  , alias:  'receiver_first'   },
            { column: 'INITCAP(mc.director_last)'   , alias:  'receiver_last'    },
            { column: 'INITCAP(mc.director_first)'  , alias:  'first'            },
            { column: 'INITCAP(mc.director_last)'   , alias:  'last'             },
            { column: 'FALSE'                       , alias:  'is_staff'         },
            { column: 'rc.roster_club_id'           , alias:  'id'               },
            {
                column: `FORMAT('%s %s', INITCAP(mc.director_first), INITCAP(mc.director_last))`,
                alias: 'receiver_name'
            },

            { column: 'INITCAP(mc.director_first)'  , alias:  'director_first'   },
            { column: 'INITCAP(mc.director_last)'   , alias:  'director_last'    },
            {
                column: `FORMAT('%s %s', INITCAP(mc.director_first), INITCAP(mc.director_last))`,
                alias: 'director_name'
            },
            { column: 'rt.roster_club_id'  , alias: 'roster_club_id'    },
        ];

        let query = this.list(
            squel.select().from('roster_team', 'rt'), sqlParams, coreColumns.concat(columns)
        )
            .group(`
                mc.director_email, mc.director_first, mc.director_last, mc.administrative_email,
                rc.roster_club_id, e.event_id, rt.roster_club_id
            `)
            .where('mc.director_email IS NOT NULL');

        if(existsQuery) {
            query.where(`NOT EXISTS(${existsQuery.clone().where('ee.roster_club_id = rc.roster_club_id').toString()})`);
        }

        if(emailSendingID) {
            query = this.__addEmailSendingHistoryJoin(query, emailSendingID, this.CLUB_DIRECTOR_RECEIVER_TYPE);
        }

        if(EmailService.emailCategory.hasUnsubscribeLink(emailCategory)) {
            query = this.commonQueryParts.excludeUnsubscribedEmails(query, 'mc.director_email', 'e.event_id');
        }

        return query;
    }

    __staffersReceiverQuery__ (coreColumns, sqlParams, existsQuery, emailSendingID, emailCategory) {
        let columns = [
            { column: 'ms.email'           , alias:  'email'            },
            { column: 'INITCAP(ms.first)'  , alias:  'receiver_first'   },
            { column: 'INITCAP(ms.last)'   , alias:  'receiver_last'    },
            { column: 'INITCAP(ms.first)'  , alias:  'first'            },
            { column: 'INITCAP(ms.last)'   , alias:  'last'             },
            { column: 'TRUE'               , alias:  'is_staff'         },
            { column: 'ms.master_staff_id' , alias:  'id'               },

            {
                column: `FORMAT('%s %s', INITCAP(ms.first), INITCAP(ms.last))`,
                alias: 'receiver_name'
            },

            { column: 'INITCAP(ms.first)'  , alias:  'director_first'   },
            { column: 'INITCAP(ms.last)'   , alias:  'director_last'    },
            {
                column: `FORMAT('%s %s', INITCAP(ms.first), INITCAP(ms.last))`,
                alias: 'director_name'
            },
            { column: 'rt.roster_club_id'  , alias: 'roster_club_id'    },
        ];

        let query = this.list(
            squel.select().from('roster_team', 'rt'), sqlParams, coreColumns.concat(columns)
        )
            .left_join('roster_staff_role', 'rsr', 'rsr.roster_team_id = rt.roster_team_id')
            .left_join('master_staff', 'ms', 'rsr.master_staff_id = ms.master_staff_id')
            .group(`
                ms.master_staff_id, ms.email, ms.first, ms.last, 
                e.event_id, rc.club_name, rt.roster_club_id
            `)
            .where('rsr.deleted IS NULL')
            .where('rsr.deleted_by_user IS NULL')
            .where('ms.deleted IS NULL')
            .where('ms.email IS NOT NULL');

        if(existsQuery) {
            query.where(
                `NOT EXISTS(${existsQuery.clone().where('ee.master_staff_id = ms.master_staff_id').toString()})`
            );
        }

        if(EmailService.emailCategory.hasUnsubscribeLink(emailCategory)) {
            query = this.commonQueryParts.excludeUnsubscribedEmails(query, 'ms.email', 'e.event_id');
        }

        if(emailSendingID) {
            query = this.__addEmailSendingHistoryJoin(query, emailSendingID, this.STAFF_RECEIVER_TYPE);
        }

        return query;
	}

	__addEmailSendingHistoryJoin (query, emailSendingID, receiversType) {
	    let emailFieldName = (receiversType === this.CLUB_DIRECTOR_RECEIVER_TYPE) ? 'mc.director_email' : 'ms.email';

        return query
            .left_join(
                'email_sending_history',
                'esh',
                `esh.email_sending_id = '${emailSendingID}' AND esh.email_address = ${emailFieldName}`
            )
            .field('esh.email_address IS NULL', 'is_unique_recipient')
            .group('esh.email_address');
    }

    getTestReceiver (eventID) {
        if (!eventID) {
            return this.TEST_RECEIVER;
        }

        const query = squel.select()
            .from('event', 'e')
            .field('e.long_name', 'event_name')
            .field('e.city', 'event_city')
            .field('e.website', 'event_website')
            .field('e.email', 'event_email')
            .field(`TO_CHAR(e.date_start, 'Month')`, 'event_month')
            .field('rc.event_id')
            .field('rc.club_name')
            .field('mc.director_first', 'receiver_first')
            .field('mc.director_last', 'receiver_last')
            .field(`CONCAT(mc.director_first, ' ', mc.director_last)`, 'receiver_name')
            .field('mc.director_first')
            .field('mc.director_last')
            .field(`CONCAT(mc.director_first, ' ', mc.director_last)`, 'director_name')
            .field(squel.str(`ARRAY_TO_STRING(ARRAY(?), ', ')`,
                squel.select()
                    .from('roster_team')
                    .field(`CONCAT(team_name)`)
                    .where('roster_club_id = rc.roster_club_id')
                    .where('deleted IS NULL')
            ), 'roster_teams')
            .field(squel.str(`ARRAY_TO_STRING(ARRAY(?), ', ')`,
                squel.select()
                    .from('roster_team', 'rst')
                    .field(`CONCAT(rst.team_name, '(', d.short_name, ')')`)
                    .join('division', 'd', 'd.division_id = rst.division_id')
                    .where('rst.roster_club_id = rc.roster_club_id')
                    .where('rst.deleted IS NULL')
            ), 'teams_divs')
            .left_join('roster_club', 'rc',
                squel.expr()
                    .and('rc.roster_club_id = ?',
                        squel.select()
                            .from('roster_club')
                            .field('roster_club_id')
                            .where('event_id = ?', eventID)
                            .order('created', false)
                            .limit(1)
                    )
            )
            .left_join('master_club', 'mc', 'mc.master_club_id = rc.master_club_id')
            .where('e.event_id = ?', eventID);

        return Db.query(query).then(({ rows: [receiver] }) => {
            if (!receiver) {
                return this.TEST_RECEIVER;
            }

            return SQLQueryBuilderUtils.replaceReceiverEmptyValuesWithTestData(receiver, this.TEST_RECEIVER);
        })
    }

    __onlineCheckinEmailReceiversList(eventID, receiversData) {
        let query = knex('event AS e')
            .select({
                event_id: 'e.event_id',
                event_website: 'e.website',
                event_name: 'e.long_name',
                event_short_name: 'e.name',
                eo_email: 'u.email',
                email: 'ms.email',
                checkin_barcode: 'ms.checkin_barcode',
                chaperone_qr_code_border: knex.raw(
                    `(e.teams_settings->>'chaperone_qr_code_border')::BOOLEAN`
                ),
                chaperone_qr_code_border_color: knex.raw(
                    `e.teams_settings->>'chaperone_qr_code_border_color'`
                ),
                receiver_first: 'ms.first',
                receiver_last: 'ms.last',
                receiver_name: knex.raw(`FORMAT('%s %s', ms.first, ms.last)`),
                roster_teams: knex.raw(`
                    STRING_AGG(rt.team_name, ', ' ORDER BY rt.organization_code, rt.team_name)
                `),
                staff_role_id: knex.raw(`COALESCE(rsr.role_id, msr.role_id)`)
            })
            .join('event_owner AS eo', 'eo.event_owner_id', 'e.event_owner_id')
            .join('user AS u', 'u.user_id', 'eo.user_id')
            .join('master_staff as ms', (table) => {
                table
                    .on('ms.master_staff_id', receiversData.master_staff_id)
                    .andOn('ms.season', sails.config.sw_season.current)
            })
            .join('event_team_checkin as etc', (table) => {
                table
                    .on('etc.event_id', 'e.event_id')
                    .andOn('etc.master_staff_id', 'ms.master_staff_id')
            })
            .join('roster_team as rt', (table) => {
                table
                    .on('rt.roster_team_id', 'etc.roster_team_id')
                    .andOn('rt.status_entry', 12)
                    .andOnNull('rt.deleted')
                    .andOn('rt.event_id', 'etc.event_id')
            })
            .leftJoin('roster_staff_role as rsr', (table) => {
                table
                    .on('rsr.roster_team_id', 'rt.roster_team_id')
                    .andOn('rsr.master_staff_id', 'etc.master_staff_id')
                    .andOnNull('rsr.deleted_by_user')
                    .andOnNull('rsr.deleted')
            })
            .leftJoin('master_staff_role as msr', (table) => {
                table
                    .on('msr.master_team_id', 'rsr.master_team_id')
                    .andOn('msr.master_staff_id', 'rsr.master_staff_id')
            })
            .where('e.event_id', eventID)
            .groupByRaw('e.event_id,rsr.role_id, msr.role_id, u.email, ms.email, ms.checkin_barcode, ms.first, ms.last');

        if(receiversData.recipient) {
            query.select({
                to: knex.raw('?', [receiversData.recipient])
            });
        } else {
            query.select({to: 'ms.email'});
        }

        return query;
    }

    emailReceiversList(eventID, receiversData) {
        if (!eventID) {
            throw new Error('Event ID required');
        }

        if (_.isEmpty(receiversData)) {
            throw new Error('Receivers Data is Empty');
        }

        const {
            DEFAULT_ONLINE_CHECKIN_TYPE,
            PRIMARY_STAFF_ONLINE_CHECKIN_TYPE,
        } = AEMService.CLUBS_GROUP_TYPE;

        if (
            [
                DEFAULT_ONLINE_CHECKIN_TYPE,
                PRIMARY_STAFF_ONLINE_CHECKIN_TYPE,
            ].includes(receiversData.template_type)
        )
            return this.__onlineCheckinEmailReceiversList(
                eventID,
                receiversData
            );

        return this.__newsletterEmailReceiversList(eventID, receiversData);
    }
}

module.exports = ClubsQueryBuilder;
