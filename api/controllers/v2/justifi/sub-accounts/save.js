// POST /api/justifi/sub-accounts/save

const JustifiService = require('../../../../services/JustifiService');

module.exports = {
    friendlyName: 'Save justifi sub account by business ID',
    description: 'Save justifi sub account by business ID',

    inputs: {
        businessId: {
            type: 'string',
            required: true,
            description: 'The ID of the business in Justifi.',
        },
    },

    exits: {
        success: {
            statusCode: 200,
        },
    },

    fn: async function (inputs, exits) {
        const user = this.req.user;

        try {
            const subAccount = await JustifiService.upsertSubAccount({
                businessId: inputs.businessId, eventOwnerId: user.event_owner_id,
            });

            exits.success(subAccount);
        } catch (err) {
            return this.res.customRespError(err);
        }
    },
};
