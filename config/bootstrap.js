'use strict';

const utils = require('../api/lib/swUtils');


module.exports.bootstrap = function(runSails) {
    sails.on('lifted', function () {

        loggers.errors_log.error('[DEBUG] syslog test');

        let sigintListener = function () {
            sails.lower(() => process.exit());
        };

        process.removeAllListeners('SIGINT', function () {});

        process.on('SIGINT', sigintListener);
        
        loggers.debug_log.warn('App connected to', utils.getDBName(sails.config.connections[sails.config.db.connection]), 'database');

        Cache.init()
            .then(() => {
                process.send('ready');
            });

        PMXMonitoring.initDbMonitoring('App', Db);
        staticHashService.init();


        Db.getListener()
        .then(listener => {
            if (process.env.pm_id === '0') {
                listener.add('match_watcher', 
                                        PGTriggersService.clearMatchCache.bind(PGTriggersService))
                loggers.debug_log.verbose('Match watcher added.');
            }

            listener.add('settings_watcher', data => { 
                try {
                    let _data = JSON.parse(data);

                    Cache.settingsWatcher(_data);
                    staticHashService.onSettingsChanged(_data);

                } catch (err) {
                    loggers.errors_log.error(err);
                }
            });
            loggers.debug_log.verbose('Settings watcher added.');
        })

        process.on('unhandledRejection', function (err, p) {
            sails.services.errorsender.defaultError(err);
        });
    });
        
    runSails();           
};
