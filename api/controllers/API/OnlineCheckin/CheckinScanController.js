'use strict';

const {REENTRY_ACTION_TYPE, SCAN_ACTION_TYPE} = require("../../../constants/online-checkin");

module.exports = {
	// GET /api/online-checkin/events
	events: function (req, res) {
		var $event = req.params.event;
		if($event !== 'events')
			return res.validation('Invalid Event Identifier');

		Db.query(
			`SELECT
   				e.event_id, e.long_name "name", e.name "short_name",
   				TO_CHAR(e.date_start, 'Mon DD, YYYY') "date_start",
   				TO_CHAR(e.date_end, 'Mon DD, YYYY') "date_end"
   			 FROM "event" e
   			 WHERE e.deleted IS NULL
   			 	AND e.allow_teams_registration = TRUE
				AND (e.date_start - INTERVAL '1 week')::DATE <= (NOW() AT TIME ZONE e.timezone)::DATE
                AND (e.date_end + INTERVAL '2 week') > (NOW() AT TIME ZONE e.timezone)
   			 ORDER BY e.date_start
			`
		).then(function (result) {
			res.status(200).json({ events: result.rows || [] });
		}).catch(function (err) {
			res.customRespError(err);
		})
	},
	// GET /api/online-checkin/:event/scan/:barcode
	scan: async function (req, res) {
		try {
            const $eventId 	= req.params.event,
			    $barcode 	= req.params.barcode,
                $season 	= sails.config.sw_season.current;

            let checkinData = {};

            if(!$eventId) {
                throw { validation: 'Invalid Event Id' };
            }

            if(!$barcode) {
                throw { validation: 'Invalid Barcode' };
            }

            const { reason, isDeactivated }  = await checkDeactivation({ eventID: $eventId, barcode: $barcode });

            if (isDeactivated) {
                let message = 'Barcode is deactivated';

                if (reason) {
                    message += ` with reason "${reason}"`;
                }

                throw { validation: message };
            }

            const mode = await getEventCheckinMode($eventId);

            if(mode && mode === 'primary_staff_barcodes') {
                checkinData = await primaryStaffScan($eventId, $barcode, $season);
            } else {
                let result = await staffScan($eventId, $barcode, $season);

                checkinData = _.first(result.rows);

                if(_.isEmpty(checkinData)) {
                    throw { validation: `No data found for barcode ${$barcode}` };
                }

                checkinData.teams.forEach(team => {
                    Db.query(
                        squel.insert().into('event_change')
                            .setFields({
                                event_id: $eventId,
                                roster_team_id: team.team_id,
                                action: 'team.online-checkin.scanned'
                            })
                    );
                });
            }

            await addScanHistory($eventId, $barcode);

            res.status(200).json(checkinData)
        } catch(err) {
            res.customRespError(err);
        }
	},
	// POST /api/online-checkin/:event/checkin/:barcode
	checkin: function (req, res) {
		var $eventId 	= req.params.event,
			$barcode 	= req.params.barcode,
			$teams 		= _.filter(req.body.teams, _.isNumber),
			season 		= sails.config.sw_season.current;

		if(!$barcode)
			return res.validation('Invalid Barcode');

        getEventCheckinMode($eventId).then(mode => {

            if(mode && mode === 'primary_staff_barcodes') {
                return primaryStaffCheckin($eventId, $barcode, season).then(staff => {
                    res.status(200).json(staff);
                }).catch(function (err) {
                    res.customRespError(err)
                })

            } else {

                if(!$teams.length) {
                    return res.validation('Expecting teams not to be empty');
                }

                Db.begin()
                    .then(function (tr) {
                        return Promise.all(
                            $teams.map(function (teamId) {
                                return __pickUpTeam(tr, teamId, $eventId, $barcode, season)
                            })
                        ).then(function (result) {
                            return tr.commit()
                                .then(function () {
                                    return result;
                                })
                        }).catch(function (err) {
                            if(err.validation && tr && !tr.isCommited) {
                                tr.rollback()
                            }
                            throw err;
                        })
                    }).then(function (pickedTeams) {
                    let totalAthletes = 0,
                        totalStaffers = 0;
                    pickedTeams.forEach(function (team) {
                        if(team) {
                            totalAthletes += parseInt(team.athletes, 10) || 0;
                            totalStaffers += parseInt(team.staff, 10) || 0;
                            team.is_picked_up = true;
                        }
                    })
                    res.ok({
                        teams 			: pickedTeams,
                        athletes 		: totalAthletes,
                        staff 			: totalStaffers
                    })
                }).catch(function (err) {
                    res.customRespError(err)
                })
            }
        })


	}

}

function __pickUpTeam (tr, rosterTeamId, eventId, barcode, season) {
	return tr.query(
		`WITH "find_team" AS (
			SELECT
				'find'::TEXT "type",
				rt.roster_team_id "team_id", rt.team_name, (
				    CASE 
				        WHEN (rt.status_checkin = 'checkedin') THEN TRUE
				        ELSE FALSE
				    END
				) "is_picked_up", (
			        SELECT COUNT(ra.*) 
			        FROM "roster_athlete" ra 
			        WHERE ra.roster_team_id = rt.roster_team_id
			            AND ra.deleted IS NULL
			            AND ra.deleted_by_user IS NULL
			            AND (ra."as_staff" = 0 OR ra."as_staff" IS NULL)
			 	) "athletes", (
			        SELECT COUNT(rsr.*)
			        FROM ( 
			            SELECT r."roster_staff_role_id", r."deleted",
			                r.deleted_by_user, COALESCE(r.primary, msr.primary) "primary",
			                r.roster_team_id
			            FROM "roster_staff_role" r 
			            LEFT JOIN "master_staff_role" msr 
			                ON msr.master_team_id = r.master_team_id
			                AND msr.master_staff_id = r.master_staff_id
			            UNION ALL 
			            SELECT a."roster_athlete_id" "roster_staff_role_id", a.deleted,
			                a.deleted_by_user, (a."as_staff" = 1) "primary", a.roster_team_id
			            FROM "roster_athlete" a 
			            WHERE a."as_staff" > 0
			        ) rsr
			        where rsr.roster_team_id = rt.roster_team_id
			            AND rsr.deleted IS NULL
			            AND rsr.deleted_by_user IS NULL
			            AND rsr.primary IS TRUE
			 	) "staff"
			FROM "master_staff" ms 
			INNER JOIN "event_team_checkin" ch 
				ON ch.master_staff_id = ms.master_staff_id
				AND ch.roster_team_id = $1 
				AND ch.event_id = $2
			INNER JOIN "roster_team" rt 
				ON rt.roster_team_id = ch.roster_team_id 
				AND rt.event_id = ch.event_id
				AND rt.deleted IS NULL 
				AND rt.status_entry = 12
			WHERE ms.checkin_barcode = $3
				AND ms.deleted IS NULL
				AND ms.season = $4
		), "update_team" as (
			UPDATE "roster_team" rt
			SET "status_checkin" = 'checkedin'
			WHERE EXISTS (
				SELECT "is_picked_up" FROM "find_team" WHERE "is_picked_up" IS FALSE
			) AND rt.roster_team_id = $1
			 AND rt.event_id = $2
			RETURNING 'update'::TEXT "type", rt.roster_team_id "team_id", rt.team_name, (
			    CASE 
			        WHEN (rt.status_checkin = 'checkedin') THEN TRUE
			        ELSE FALSE
			    END
			) "is_picked_up", 0 "athletes", 0 "staff"
		)
		SELECT * from "find_team" UNION ALL SELECT * FROM "update_team"`,
		[rosterTeamId, eventId, barcode, season]
	).then(function (result) {
		var rowCount = result.rowCount,
			resultRow;
		if(rowCount === 0) {
			throw {
				validation: `Team #${rosterTeamId} not found`
			}
		}
		if(rowCount === 1) {
			resultRow = _.first(result.rows);
			throw {
				validation: `Team "${resultRow.team_name}" is already picked up`
			}
		}
		if(rowCount === 2) {
            Db.query(
                squel.insert().into('event_change')
                    .setFields({
                        event_id: eventId,
                        roster_team_id: rosterTeamId,
                        action: 'team.checkin.checkedin'
                    })
            );

			resultRow = result.rows[0];
			return _.omit(resultRow, 'type');
		}

		throw {
			validation  : 'Operation Failed',
			description : 'More than 2 rows returned by check in action',
			data 		: result.rows
		}
	})
}

function getEventCheckinMode (eventID) {
    let query = `SELECT online_team_checkin_mode FROM event WHERE "event_id" = $1`;

    return Db.query(query, [eventID])
        .then(result => result.rows[0] && result.rows[0].online_team_checkin_mode || null);
}

function primaryStaffScan (eventID, barcode, season) {
    let query =
        `SELECT ms.first, ms.last,
          (
            CASE
              WHEN etc.date_scanned IS NULL THEN 1
              ELSE 0
            END
          ) "total_staff",
          rt.team_name,
          rt.roster_team_id,
          COUNT(ra.roster_athlete_id) FILTER (
            WHERE ra.deleted IS NULL
                  AND ra.deleted_by_user IS NULL
                  AND (ra."as_staff" = 0 OR ra."as_staff" IS NULL)
          ) "athletes",
          etc.date_scanned AT TIME ZONE e.timezone AS date_scanned
        FROM master_staff ms
          JOIN event e
            ON e.event_id = $1 OR $1 = ANY ((e.teams_settings ->> 'online_checkin_related_events')::INT[])
          JOIN event_team_checkin etc
            ON etc.master_staff_id = ms.master_staff_id
            AND etc.event_id = e.event_id
          LEFT JOIN roster_team rt
            ON rt.roster_team_id = etc.roster_team_id
            AND rt.event_id = etc.event_id
          LEFT JOIN roster_athlete ra
            ON ra.roster_team_id = rt.roster_team_id
        WHERE ms.checkin_barcode = $2 
            AND ms.deleted IS NULL 
            AND ms.season = $3
        GROUP BY ms.first, ms.last, etc.date_scanned,
          rt.team_name, rt.roster_team_id, e.event_id
        ORDER BY e.event_id = $1
        LIMIT 1`;

    return Db.query(query, [eventID, barcode, season]).then(result => result.rows[0] || null)
        .then(data => {
            if(!data) {
                throw { validation: `No data found for barcode ${barcode}` };
            }

            return {
                staff_data      : `${data.first}, ${data.last}`,
                total_athletes  : data.athletes,
                total_staff     : data.total_staff,
                teams: [{
                    team_name           : data.team_name,
                    team_id             : data.roster_team_id,
                    is_picked_up        : !!data.date_scanned,
                    show_alert_message  : false,
                    team_alert_note     : null,
                    online_checkin_date : data.date_scanned,
                    athletes            : 0,
                    staff               : 1
                }]
            }
        })
}

function primaryStaffCheckin (eventID, barcode, season) {
    let query =
        `WITH team_checkin AS (
            SELECT DISTINCT
              etc.event_team_checkin_id,
              etc.date_scanned,
              rt.team_name,
              rt.roster_team_id
            FROM master_staff ms
              LEFT JOIN event_team_checkin etc
                ON etc.master_staff_id = ms.master_staff_id
              LEFT JOIN roster_team rt
                ON etc.roster_team_id = rt.roster_team_id    
            WHERE ms.checkin_barcode = $1
                  AND etc.event_id = $2
                  AND ms.deleted IS NULL
                  AND ms.season = $3
        ), updated AS (
          UPDATE event_team_checkin etc
          SET date_scanned = NOW()
          WHERE etc.event_team_checkin_id = (
            SELECT tc.event_team_checkin_id
            FROM team_checkin tc
            WHERE tc.date_scanned IS NULL
          )
          RETURNING etc.date_scanned
        )
        SELECT
          COALESCE(
            (SELECT date_scanned FROM team_checkin),
            (SELECT date_scanned FROM updated)
          )    "date_scanned",
          ( SELECT team_name FROM team_checkin ) "team_name",
          ( SELECT roster_team_id FROM team_checkin ) "roster_team_id",
          ( SELECT date_scanned FROM updated ) IS NOT NULL "success"`;

    return Db.query(query, [barcode, eventID, season]).then(result => result.rows[0] || null)
        .then(data => {

            if(!data) {
                throw {
                    validation: `Team #${data.roster_team_id} not found`
                }
            }

            if(data && !data.success && !data.roster_team_id) {
                throw { validation: `No data found for barcode ${barcode}` };
            }

            if(data && !data.success && data.team_name) {
                throw {
                    validation: `Team "${data.team_name}" is already picked up`
                }
            }

            return {
                teams: [{
                    team_name   : data.team_name,
                    team_id     : data.roster_team_id,
                    is_picked_up: data.success,
                    athletes            : 0,
                    staff               : 0,
                    type                : 'update'
                }],
                athletes 		: 0,
                staff 			: 1
            }
        })
}

function staffScan($eventId, $barcode, $season) {
    return Db.query(
        `SELECT 
            "staff_data".*, (
                SELECT
                    SUM(
                        ("t"->>'athletes')::INTEGER
                    )
                FROM JSON_ARRAY_ELEMENTS("staff_data"."teams") "t"
                WHERE ("t"->>'is_checkedin')::BOOLEAN IS NOT TRUE
            ) "total_athletes", (
                SELECT
                    SUM(
                        ("t"->>'staff')::INTEGER
                    )
                FROM JSON_ARRAY_ELEMENTS("staff_data"."teams") "t"
                WHERE ("t"->>'is_checkedin')::BOOLEAN IS NOT TRUE
            ) "total_staff"
        FROM (
            SELECT
                FORMAT('%s, %s', ms.last, ms.first) "staff_name", (
                    SELECT ARRAY_TO_JSON(ARRAY_AGG(ROW_TO_JSON("t")))
                    FROM (
                        SELECT 
                            rt.team_name, rt.roster_team_id "team_id", (
                                CASE 
                                    WHEN (rt.status_checkin = 'checkedin') THEN TRUE
                                    ELSE FALSE
                                END
                            ) "is_picked_up",
                            (
                                CASE 
                                    WHEN (rt.status_checkin = 'alert') THEN TRUE
                                    ELSE FALSE
                                END
                            ) "show_alert_message",
                            rt.team_alert_note,
                            rt.online_checkin_date AT TIME ZONE e.timezone AS online_checkin_date, (
                                SELECT COUNT(ra.*) 
                                FROM "roster_athlete" ra 
                                WHERE ra.roster_team_id = rt.roster_team_id
                                    AND ra.deleted IS NULL
                                    AND ra.deleted_by_user IS NULL
                                    AND (ra."as_staff" = 0 OR ra."as_staff" IS NULL)
                            ) "athletes", (
                                SELECT COUNT(rsr.*) 
                                FROM ( 
                                    SELECT r."roster_staff_role_id", r."deleted",
                                        r.deleted_by_user, COALESCE(r.primary, msr.primary) "primary",
                                        r.roster_team_id
                                    FROM "roster_staff_role" r 
                                    LEFT JOIN "master_staff_role" msr 
                                        ON msr.master_team_id = r.master_team_id
                                        and msr.master_staff_id = r.master_staff_id
                                    UNION ALL 
                                    SELECT a."roster_athlete_id" "roster_staff_role_id", a.deleted,
                                        a.deleted_by_user, (a."as_staff" = 1) "primary", a.roster_team_id
                                    FROM "roster_athlete" a 
                                    WHERE a."as_staff" > 0
                                ) rsr
                                where rsr.roster_team_id = rt.roster_team_id
                                    AND rsr.deleted IS NULL
                                    AND rsr.deleted_by_user IS NULL
                                    AND rsr.primary IS TRUE
                            ) "staff"
                        FROM "event_team_checkin" etch 
                        INNER JOIN "roster_team" rt 
                            ON etch.roster_team_id = rt.roster_team_id
                                AND etch.event_id = rt.event_id
                                AND rt.deleted IS NULL
                                AND rt.status_entry = 12
                        INNER JOIN event AS e
                            ON e.event_id = $1
                        WHERE etch.master_staff_id = ms.master_staff_id
                            AND etch.event_id = $1
                        GROUP BY etch.event_team_checkin_id, rt.roster_team_id, e.timezone
                    ) "t"
                ) "teams"
            FROM "master_staff" ms
            WHERE ms.checkin_barcode = $2
                AND ms.deleted IS NULL
                AND ms.season = $3
        ) "staff_data"
        WHERE "staff_data".teams IS NOT NULL`,
        [$eventId, $barcode, $season]
    )
};

function checkDeactivation({ eventID, barcode }) {
    const query = knex('master_staff AS ms')
        .select(
            {
                reason: knex.raw(`(
                    SELECT comments 
                    FROM event_change AS ec
                    WHERE ec.event_id = ?
                        AND ec.master_staff_id = ms.master_staff_id
                        AND action = 'team.roster.staff.checkin.deactivated'
                    ORDER BY created DESC
                    LIMIT 1
                )`, [eventID])
            },
            {
                is_deactivated: knex.raw(`(
                    SELECT etc.deactivated_at 
                    FROM event_team_checkin AS etc
                    WHERE etc.event_id = ?
                        AND etc.master_staff_id = ms.master_staff_id
                    LIMIT 1
                )`, [eventID])
            }
        )
        .where('ms.checkin_barcode', barcode);

    return Db.query(query).then(({ rows: [row] }) => {
        return {
            reason: row && row.reason,
            isDeactivated: row && row.is_deactivated
        }
    })
}

async function addScanHistory (eventId, barcode) {
    const scanHistoryData = await __prepareScanHistoryData(eventId, barcode);

    return Db.query(knex('online_checkin_api_history').insert(scanHistoryData));
}

async function __prepareScanHistoryData (eventId, barcode) {
     const isScanned = await __isBarcodeScanned(eventId, barcode);
     const actionType = isScanned ? REENTRY_ACTION_TYPE : SCAN_ACTION_TYPE;

    return {
        barcode,
        event_id: eventId,
        action_type: actionType,
    }
}

function __isBarcodeScanned (eventId, barcode) {
    const query = knex('online_checkin_api_history as ocah')
        .select('ocah.online_checkin_api_history_id')
        .where('ocah.barcode', barcode)
        .where('ocah.event_id', eventId)
        .where('ocah.action_type', SCAN_ACTION_TYPE)

    return Db.query(query).then(({ rowCount }) => rowCount > 0);
}


