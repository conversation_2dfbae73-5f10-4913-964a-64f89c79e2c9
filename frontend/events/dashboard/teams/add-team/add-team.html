<div class="modal-header">
    <h4 class="text-center">Add Teams to Event</h4>
</div>
<div class="modal-body">
    <form class="form-inline">
        <div class="form-group">
            <sw-searchbox
                placeholder="Club Code ..."
                input-model="params.club"
                reload="loadTeamsList()"
            ></sw-searchbox>
        </div>
        <div class="form-group">
            <sw-searchbox
                placeholder="Team USAV ..."
                input-model="params.team"
                reload="loadTeamsList()"
            ></sw-searchbox>
        </div>
    </form>
    <div class="form-group" style="margin-top: 15px; margin-bottom: 15px;">
        <div class="radio">
            <label>
                <input type="radio" ng-model="params.sendEmails" value="true" checked>
                Yes
            </label>
        </div>
        <div class="radio">
            <label>
                <input type="radio" ng-model="params.sendEmails" value="false">
                No
            </label>
        </div>
        <label>Send email notification for CD about entering status</label>
    </div>
    <table class="table table-condensed">
        <thead>
            <tr>
                <th>G</th>
                <th>Team Name</th>
                <th>USAV</th>
                <th>Rank</th>
                <th>Club Name</th>
                <th>Club Code</th>
                <th></th>
            </tr>
        </thead>
        <tbody>
            <tr ng-repeat="t in data.teams">
                <td>
                    <genders
                    m="t.gender !== 'female'"
                    f="t.gender !== 'male'"
                    ></genders>
                </td>
                <td ng-bind="t.team_name"></td>
                <td ng-bind="t.team_code"></td>
                <td ng-bind="t.rank"></td>
                <td ng-bind="t.club_name"></td>
                <td ng-bind="t.club_code"></td>
                <td>
                    <i class="fa fa-check" ng-if="t.entered"></i>
                    <div uib-dropdown ng-if="!t.entered">
                        <button class="btn btn-success btn-xs" uib-dropdown-toggle>Add</button>
                        <ul class="dropdown-menu" role="menu" aria-labelledby="entry">
                            <li role="presentation" class="{{d.gender}}" ng-repeat="d in utils.divisions">
                                <a role="menuitem" tabindex="-1" href="" ng-click="assignTeam(d.division_id, t)">
                                    <genders
                                    m="d.gender !== 'female'"
                                    f="d.gender !== 'male'"
                                    ></genders>
                                    {{ d.name }}
                                </a>
                            </li>
                        </ul>
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
</div>
<div class="modal-footer">
    <button class="btn btn-default pull-right" ng-click="$parent.$close()">Close</button>
</div>
