'use strict';

const argv  = require('optimist').argv;
const pg    = require('pg');
const squel = require('../api/services/squel');
const fs    = require('mz/fs');
const xlsx  = require('xlsx');
const assert= require('assert');
const utils = require('../api/lib/swUtils');

const {
    event           : EVENT_ID,
    owner           : EO_ID,
    user            : USER_ID,
    title           : LIST_TITLE,
    s3path          : S3_PATH,
    path            : PATH_TO_FILE,
    conn            : DB_CONNECTION,
    import_option   : IMPORT_OPTION,
    list_id         : LIST_ID,
    mode            : MODE
} = argv;

const EMAIL_REGEX = /^\w+(?:[._+-]\w+)*@\w+(?:[_.-]\w+)*\.[a-zA-Z]{2,4}$/;

const DOCUMENT_HEADING = {
    FIRST: 'First Name',
    LAST : 'Last Name',
    EMAIL: 'Email'
};

const UPDATE_MODE           = 'update';
const IMPORT_OPTION_VALUE   = {
    DELETE: 'delete',
    UPDATE: 'update'
};

const IS_UPDATE_MODE = (MODE === UPDATE_MODE);

validateParams();

const connectionParams  = JSON.parse(Buffer.from(DB_CONNECTION, 'base64').toString('utf-8'));
const dbClient          = new pg.Client(connectionParams);

dbClient.connect();

function updateListRow () {
    let query = squel.update().table('custom_recipients_list', 'crl')
        .where('crl.custom_recipients_list_id = ?', LIST_ID)
        .returning('custom_recipients_list_id "list_id", title, event_id IS NULL "is_for_all_events"');

    if(IMPORT_OPTION === IMPORT_OPTION_VALUE.DELETE) {
        query.set('document_path', S3_PATH);
    } else {
        query.set(`document_path = (crl."document_path" || ';' || '${S3_PATH}')`);
    }

    let { text: sql, values } = query.toParam();

    return dbClient.query(sql, values)
        .then(result => result.rows[0] && result.rows[0] || null);
}

function deleteRecipientsRows (listID) {
    let query = squel.delete().from('custom_recipient')
        .where('custom_recipients_list_id = ?', listID);

    let { text: sql, values } = query.toParam();

    return dbClient.query(sql, values).then(result => result.rowCount);
}

function createListRow () {
    let query = squel.insert().into('custom_recipients_list')
        .set('title'            , LIST_TITLE)
        .set('document_path'    , S3_PATH)
        .set('event_owner_id'   , Number(EO_ID))
        .set('user_id'          , Number(USER_ID))
        .returning('custom_recipients_list_id "list_id", title, event_id IS NULL "is_for_all_events"');

    if(!isNaN(Number(EVENT_ID))) {
        query.set('event_id', EVENT_ID);
    }

    let { text: sql, values } = query.toParam();

    return dbClient.query(sql, values)
        .then(result => result.rows[0] && result.rows[0] || null);
}

async function processImport() {
    try {
        await dbClient.query('BEGIN;');

        const [file, list]  = await Promise.all([ readFile(PATH_TO_FILE),
            IS_UPDATE_MODE ? updateListRow() : createListRow()
        ]);
        let parsedRows      = parseFile(file, list.list_id);

        if(!list) {
            throw new Error(`Can't ${IS_UPDATE_MODE ? 'find' : 'create'} contacts list`);
        }

        if(!parsedRows.length) {
            throw new Error('Empty list passed');
        }

        if(IMPORT_OPTION === IMPORT_OPTION_VALUE.DELETE) {
            await deleteRecipientsRows(LIST_ID);
        }

        let splittedRows        = utils.splitArray(parsedRows, 200);
        let createdRecipients   = [];

        await splittedRows.reduce((prev, chunk) => {
            return prev.then(() => createRecipientsRows(chunk)
                .then(created => createdRecipients = createdRecipients.concat(created))
            )
        }, Promise.resolve());

        if(createdRecipients.length === 0) {
            throw new Error('0 new contacts in list');
        }

        await dbClient.query('COMMIT;');

        return { parsed: parsedRows.length, added: createdRecipients.length, list };
    } catch (err) {
        await dbClient.query('ROLLBACK;');

        throw err;
    }
}

function createRecipientsRows (rows) {
    let query = squel.insert().into('custom_recipient')
        .setFieldsRows(rows)
        .returning('email, first, last')
        .onConflict(['email', 'custom_recipients_list_id'], {});

    let { text: sql, values } = query.toParam();

    return dbClient.query(sql, values).then(result => result.rows);
}

function parseFile(file, listID) {
    let workbook = xlsx.read(file);

    let workingSheet = workbook.Sheets[workbook.SheetNames[0]];
    let rawJSON      = xlsx.utils.sheet_to_json(workingSheet, { header: 1 });

    return formatContactRows(rawJSON, listID);
}

function formatContactRows (parsedFile, listID) {
    let headings = parsedFile[0];

    let firstIndex = headings.indexOf(DOCUMENT_HEADING.FIRST),
        lastIndex  = headings.indexOf(DOCUMENT_HEADING.LAST),
        emailIndex = headings.indexOf(DOCUMENT_HEADING.EMAIL);

    if(emailIndex < 0) {
        throw new Error('Email column not found');
    }

    return parsedFile.reduce((all, row, index) => {
        //skip first row - Headings
        if(index > 0) {
            let email = row[emailIndex] && row[emailIndex].trim().toLowerCase();
            let first = row[firstIndex] && row[firstIndex].trim() || null;
            let last  = row[lastIndex]  && row[lastIndex].trim()  || null;

            if(email) {
                if(!EMAIL_REGEX.test(email)) {
                    throw new Error(
                        `Email in list is not valid: at row #${index+1}. Please, update the list and try import again.`
                    );
                }

                all.push({
                    email,  first, last,
                    custom_recipients_list_id: listID,
                    document_row             : JSON.stringify(row)
                });
            }
        }

        return all;
    }, [])
}

function readFile (filePath) {
    return fs.readFile(filePath);
}

function validateParams () {
    try {
        assert(EO_ID && !isNaN(Number(EO_ID))       , 'Invalid EO Identifier');
        assert(USER_ID && !isNaN(Number(USER_ID))   , 'Invalid user Identifier');
        assert(LIST_TITLE                           , 'Invalid list title');
        assert(S3_PATH                              , 'Invalid S3 path');
        assert(PATH_TO_FILE                         , 'Invalid file path');
        assert(DB_CONNECTION                        , 'Invalid DB connection string');

        if(MODE && MODE === IS_UPDATE_MODE) {
            assert(LIST_ID && !isNaN(Number(LIST_ID)), 'Invalid list ID');
            assert(IMPORT_OPTION && [IMPORT_OPTION_VALUE.UPDATE, IMPORT_OPTION_VALUE.DELETE].includes(IMPORT_OPTION)
                , 'Invalid mode value');
        }

    } catch (error) {
        process.stderr.write(error.message, () => process.exit(1));
    }
}

processImport()
    .then(result => {
        process.stdout.write(JSON.stringify({result}));
        process.exit(0);
    })
    .catch(err => {
        dbClient.end();

        process.stderr.write(err.message, () => process.exit(1));
    });
