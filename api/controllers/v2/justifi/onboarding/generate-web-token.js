// POST /api/justifi/onboarding/generate-web-token

const JustifiService = require('../../../../services/JustifiService');

module.exports = {
    friendlyName: 'Generate onboarding web token',
    description: 'Generate onboarding web token for Justifi',

    inputs: {},

    exits: {
        success: {
            statusCode: 200,
        },
    },

    fn: async function (inputs, exits) {
        const user = this.req.user;

        try {
            const { businessId } = await JustifiService.createBusiness({
                email: user.email,
                legalName: `${user.first} ${user.last}`,
            });

            const token = await JustifiService.createWebTokenForBusiness({
                businessId,
            });

            exits.success({ token, businessId });
        } catch (err) {
            return this.res.customRespError(err);
        }
    },
};
