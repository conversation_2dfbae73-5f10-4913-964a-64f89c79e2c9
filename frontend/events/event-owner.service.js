angular.module('SportWrench').service('EventOwnerService', ['$uibModal', '$http', 'StripeService', 'toastr', EventOwnerService]);


function EventOwnerService ($uibModal, $http, StripeService, toastr) {
	this._$uibModal 		= $uibModal;
	this._$http 			= $http;
	this._baseUrl 			= '/api/eo'; 
	this._StripeService 	= StripeService;
	this._toastr 			= toastr;
}

EventOwnerService.prototype.saveAcc = function (data) {
	return this._$http.post(this._baseUrl + '/stripe-acc', data).then(function (resp) {
		return resp.data.account;
	});
};

EventOwnerService.prototype.saveJustifiSubAccount = function (data) {
	return this._$http.post(this._baseUrl + '/justifi-sub-accounts', data).then(function (resp) {
		return resp.data;
	});
};

EventOwnerService.prototype.getEOJustifiSubAccounts = function (eventID, type) {
	var params = {};

	if (eventID) {
		params.params = { event: eventID };
	}

	return this._$http.get(this._baseUrl + '/justifi-sub-accounts/' + type, params)
		.then(function (resp) {
			return resp.data.accounts;
		});
};


EventOwnerService.prototype.getEOAccounts = function (eventID, type) {
	var params = {};

	if (eventID) {
		params.params = { event: eventID };
	}

	return this._$http.get(this._baseUrl + '/stripe-accounts/' + type, params)
	.then(function (resp) {
		return resp.data.accounts;
	});
};

EventOwnerService.prototype.getEOTilledAccounts = function (eventID, type) {
	var params = {};

	if (eventID) {
		params.params = { event: eventID };
	}

	return this._$http.get(this._baseUrl + '/tilled-accounts/' + type, params)
	.then(function (resp) {
		return resp.data.accounts;
	});
};

EventOwnerService.prototype.openStripeAccModal = function () {
	var _self = this;

	return this._$uibModal.open({
		templateUrl : 'events/stripe/accounts-modal.html',
		controller 	: ['$scope', function ($scope) {
			var _livePlatformClientID = null,
				_testPlatformClientID = null;

			$scope.modalTitle 	= '<h4>Stripe Accounts</h4>';
			$scope.accounts 	= [];
			$scope.newAccount 	= {};
			$scope.tabs 		= { active: 0 };

			$scope.loadAccs = function () {
				$scope.isLoading = true;

				_self.getEOAccounts(null, 'tickets').then(function (accounts) {
					$scope.accounts = accounts;
				}).finally(function () {
					$scope.isLoading = false;
				});
			};

			$scope.connect = function (account) {
				var clientID = account.is_test?_testPlatformClientID:_livePlatformClientID;

				_self._StripeService.connect(clientID, account.is_test, account.email);
			};

			$scope.saveAccount = function (data) {
				return _self.saveAcc(data)
				.then(function () {
					$scope.loadAccs();
					$scope.tabs.active = 0;
					_self._toastr.success('Saved!');

					return {};
				});
			};

			$scope.loadAccs();

			_self._StripeService.getPlatformClientID()
			.then(function (data) {
				_livePlatformClientID = data.live;
				_testPlatformClientID = data.test;
			});
		}]
	}).result;
};


EventOwnerService.prototype.openJustifiAccModal = function () {
	var _self = this;

	return this._$uibModal.open({
		templateUrl : 'events/justifi/accounts-modal.html',
		controller 	: ['$scope', function ($scope) {

			$scope.modalTitle 	= '<h4>Justifi Accounts</h4>';
			$scope.accounts 	= [];
			$scope.newAccount 	= {};
			$scope.tabs 		= { active: 0 };

			$scope.loadAccs = function () {
				$scope.isLoading = true;

				_self.getEOJustifiSubAccounts(null, 'tickets').then(function (accounts) {
					$scope.accounts = accounts;
				}).finally(function () {
					$scope.isLoading = false;
				});
			};

			$scope.saveAccount = function (businessId) {
				console.log({ businessId }, 'event owner service: submitSuccess');

				return _self.saveJustifiSubAccount({ businessId })
				.then(function () {
					$scope.loadAccs();
					$scope.tabs.active = 0;
					_self._toastr.success('Saved!');

					return {};
				});
			};

			$scope.loadAccs();			
		}]
	}).result;
};
