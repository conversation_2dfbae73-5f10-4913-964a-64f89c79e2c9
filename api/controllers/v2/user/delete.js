module.exports = {
    friendlyName: 'Delete user account',
    description: 'Delete user account',

    exits: {
        accepted: {
            statusCode: 202,
        },
    },

    fn: async function (inputs, exits) {
        const userId = Number(this.req.session.passport.user.user_id);

        try {
            await UserService.reg.deleteUser(userId);

            clearSession(this.req, this.res);

            exits.accepted({ message: 'OK' });
        } catch (err) {
            this.res.customRespError(err);
        }
    },
};

function clearSession(req, res) {
    res.clearCookie('remember_me');
    res.clearCookie('sails.sid');

    if (req.user && req.user.user_id && req.sessionID) {
        RedisService.delUserDataMonitorKey(
            req.user.user_id,
            req.sessionID
        ).catch(ErrorSender.defaultError.bind(ErrorSender));
    }

    req.logout(function (err) {
        if (err) {
            loggers.errors_log.error(err);
        }
        req.session.destroy(function (err) {
            if (err) {
                loggers.errors_log.error(err);
            }
        });
    });
}
