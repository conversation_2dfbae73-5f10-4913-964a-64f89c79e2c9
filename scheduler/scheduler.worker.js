'use strict';

/**
 * Docs: https://docs.google.com/document/d/137m4-iWEXzWqLI7ZAZ4bU1dOCBiId99qpBC0vDuYxYk/edit#
 */

/* Load sails app services to global */
require('./globals-loder').init();

const fs                     = require('fs');
const optimist               = require('optimist');
const RosterDeadlineChecker  = require('../api/lib/RosterDeadlineChecker');

const UncollectedSWFeePaymentRunner = require('./tasks/pay-uncollected-fee');
const AdditionalFeesPaymentRunner = require('./tasks/pay-additional-fees');
const DeletePendingPaymentSessionsRunner = require('./tasks/delete-pending-payment-sessions');

const WebpointSyncTask = require('./tasks/fill-members-tables-with-webpoint-values');

const PendingPaymentsReceiptSender = require('./tasks/send-paid-pending-payments');

const SendUpcomingUncollectedSWFeeChargesNotification = require('./tasks/send-upcoming-uncollected-sw-fee-charges-notifications');

const WP_OUTPUT_FILENAME = require('path').resolve(
    __dirname, '..', '..', 'webpoint', `webpoint_output_${(optimist.argv.dev)?'dev':'prod'}.json`);

const ResetScannedTickets = require('./tasks/reset-scanned-weekend-tickets');

const EmailRecipientsListGeneration = require('./tasks/email-recipients-list-generation');

const EmailMassSending = require('./tasks/email-mass-sending/email-mass-sending');

const SportEngineClubImport = require('../api/services/SportEngineMemberService').import;
const AauClubImport = require('../api/services/AauMemberService').import;

const SESyncProcess = require('./tasks/sync-usav-members-from-se');
const SEUtils = require('../api/lib/SEUtilsService');

const {PAYMENT_FOR} = require('../api/constants/payments');

try {
    loggers.debug_log.verbose('Checking WP Output File Exists', WP_OUTPUT_FILENAME);
    if (fs.existsSync(WP_OUTPUT_FILENAME)) {
        fs.unlinkSync(WP_OUTPUT_FILENAME);
        loggers.wp.verbose(`Unlinking WP parser output file (${WP_OUTPUT_FILENAME}) on server start finished`);
    }
} catch (e) {
    loggers.wp.error(`Removing ${WP_OUTPUT_FILENAME} on server start error`, e.message);
}

PMXMonitoring.initDbMonitoring('App', Db);

Cache.init();

if (optimist.argv.prod) {
    runCronTasks();

} else if (optimist.argv.dev) {

    // sendPaidPendingPayments(); temporarily turn off on 18.09.2018
    sportEngineImportTask();
    aauImportTask();
    sendNotifications();
    massEmailSendingReceiversListGeneration();
    massEmailSending()
    uncollectedSWFeePayments();
    lostDisputeFeeFailedACHFeePayments();
    deletePendingPaymentSessions();
    syncUSAVMemberFromSEAPI();
}

function runCronTasks () {
    loggers.debug_log.verbose('Starting all cron tasks...');
    // fetchMembersDataFromWebpointTask();
    sportEngineImportTask();
    aauImportTask();
    setLockedPassDeadlineTeamsAndScoreEnryChange();
    sendPaidPendingPayments(); // was temporarily turned off on 18.09.2018
    sendNotifications();
    massEmailSendingReceiversListGeneration();
    massEmailSending();
    uncollectedSWFeePayments();
    lostDisputeFeeFailedACHFeePayments();
    deletePendingPaymentSessions();
    syncUSAVMemberFromSEAPI();

    /* Every 5 minutes */
    cronNotifications.start_task('*/5 * * * *', function () {
        RosterSnapshotService.copyMasterValues(sails.config.sw_season.current)
            .catch(function (err) {
                loggers.errors_log.error(err);
            });
    });
}

function sendNotifications () {
    // CRON Immediately task - sending notifications every 15 minutes
    cronNotifications.start_task('*/15 * * * *', function() {
        /* Send EOs an email if there are ticket type price changes in a day interval */
        TicketsCron.notifyEO()
            .then(qty => {
                loggers.debug_log.verbose('Sent', qty, 'notifications to EO about auto price change of ticket types')
            })
            .catch(loggers.errors_log.error.bind(loggers.errors_log));
    });

    /* Run Cron at every minute */
    cronNotifications.start_task('*/1 * * * *', function() {
        eventNotifications.send_notifications_cd()
            .catch((err) => loggers.errors_log.error('CRON Task Error (club director)', err));
    });

    // CRON Hourly task - sending notifications every hour
    cronNotifications.start_task('0 */1 * * *', function() {
        // loggers.event_notifications.info('Run hourly cron task');
        TicketsCron.setCurrentPrice()
            .then(() => {})
            .catch(err => {
                loggers.errors_log.error('Tickets cron error', err);
            })
    });

    // Run Cron at every 5 minutes
    cronNotifications.start_task('*/5 * * * *', () => {
        SendUpcomingUncollectedSWFeeChargesNotification.run()
            .then(() => {
            })
            .catch(function (err) {
                loggers.errors_log.error('Upcoming uncollected SW Fee charges error', err);
            });
    })
}

function resetWeekendTicketsScan() {
    let isFulfilled = true;

    // reset scanned weekend tickets
    cronNotifications.start_task('59 */1 * * *', () => {
        if (!isFulfilled) {
            return;
        }

        isFulfilled = false;

        ResetScannedTickets.run()
            .then(() => {
                isFulfilled = true;
            })
            .catch(function (err) {
                loggers.errors_log.error(err);
            });
    });
}

function sendPaidPendingPayments() {
    let isFulfilled = true;

    cronNotifications.start_task('*/5 * * * * *', () => {
        if (!isFulfilled) {
            return;
        }

        isFulfilled = false;

        PendingPaymentsReceiptSender.processSend()
            .then(() => {
                isFulfilled = true;
            })
            .catch(function (err) {
                isFulfilled = true;
                loggers.errors_log.error(err);
            });
    })
}

function resetWeekendTicketsScanDev() {
    let isFulfilled = true;

    // reset scanned weekend tickets on dev every 5 minutes
    cronNotifications.start_task('*/5 * * * *', () => {
        if (!isFulfilled) {
            return;
        }

        isFulfilled = false;

        ResetScannedTickets.run()
            .then(() => {
                isFulfilled = true;
            })
            .catch(function (err) {
                loggers.errors_log.error(err);
            });
    });
}

function setLockedPassDeadlineTeamsAndScoreEnryChange() {
    let season                      = sails.config.sw_season.current,
        rosterDeadlineChecker       = new RosterDeadlineChecker(season),
        checkDeadline               = rosterDeadlineChecker.processCheck.bind(rosterDeadlineChecker),
        tz   = 'America/New_York';

    // lock team roster, when roster deadline passed (hourly task)
    cronNotifications.start_task('5 */1 * * *', checkDeadline, tz);
}

function fetchMembersDataFromWebpointTask () {
    let rowsLimitForDailyTask   = 2500,
        rowsLimitForHourlyTask  = 200,
        tz                      = 'America/New_York';

    let runTask = (rowsLimit) => {
        let scriptTimeStart = Date.now();

        let eventID = null;
        WebpointSyncTask.run(eventID, rowsLimit)
        .then(({updatedRowsCount, errors}) => {
            let scriptTimeEnd = Date.now();

            let secondsInWork = (scriptTimeEnd - scriptTimeStart) / 1000;

            return EmailService.sendEmail({
                from        : '"SportWrench" <<EMAIL>>',
                to          : '"sw debug" <<EMAIL>>',
                subject     : 'Webpoint synchronization cron result',
                text        :
                    `Updated ${updatedRowsCount} "master_athlete/staff" rows in ${secondsInWork} sec.`
            });
        })
        .catch(err => {
            loggers.errors_log.error(err);
        })
    }

    cronNotifications.start_task('00 45 01 * * 0-6', () => {
        runTask(rowsLimitForDailyTask)
    }, tz);
    cronNotifications.start_task('7 */1 5-22 * *',   () => {
        runTask(rowsLimitForHourlyTask)
    }, tz);
}

function massEmailSendingReceiversListGeneration () {

    let isFulfilled = true;

    //  proceed email receivers list generation every 30 seconds
    cronNotifications.start_task('*/30 * * * * *', () => {
        if (!isFulfilled) {
            return;
        }

        isFulfilled = false;

        EmailRecipientsListGeneration.run()
            .then(() => {
                isFulfilled = true;
            })
            .catch(function (err) {
                isFulfilled = true;
                loggers.errors_log.error(err);
            });
    });
}

function massEmailSending () {

    let isFulfilled = true;

    //  proceed email sending every 10 seconds
    cronNotifications.start_task('*/10 * * * * *', () => {
        if (!isFulfilled) {
            return;
        }

        isFulfilled = false;

        EmailMassSending.run()
            .then(() => {
                isFulfilled = true;
            })
            .catch(function (err) {
                isFulfilled = true;
                loggers.errors_log.error(err);
            });
    });
}

function sportEngineImportTask () {
    let isFulfilled = true;

    cronNotifications.start_task('*/10 * * * * *', () => {
        if (!isFulfilled) {
            return;
        }

        isFulfilled = false;

        SportEngineClubImport.process.run()
            .catch(err => loggers.errors_log.error(err))
            .finally(() => isFulfilled = true)
    });
}

function aauImportTask () {
    let isFulfilled = true;

    cronNotifications.start_task('*/10 * * * * *', () => {
        if (!isFulfilled) {
            return;
        }

        isFulfilled = false;

        AauClubImport.process.run()
            .catch(err => loggers.errors_log.error(err))
            .finally(() => isFulfilled = true)
    });
}

function uncollectedSWFeePayments () {
    let season = sails.config.sw_season.current;

    let ticketsTaskFulfilled = true;
    let teamsTaskFulfilled = true;
    let boothsTaskFulfilled = true;

    cronNotifications.start_task('0 6 * * *', () => {
        if (!ticketsTaskFulfilled) {
            return;
        }

        ticketsTaskFulfilled = false;

        UncollectedSWFeePaymentRunner.run(PAYMENT_FOR.TICKETS, season)
            .catch(err => loggers.errors_log.error(err))
            .finally(() => ticketsTaskFulfilled = true);
    });

    cronNotifications.start_task('0 7 * * *', () => {
        if (!teamsTaskFulfilled) {
            return;
        }

        teamsTaskFulfilled = false;

        UncollectedSWFeePaymentRunner.run(PAYMENT_FOR.TEAMS, season)
            .catch(err => loggers.errors_log.error(err))
            .finally(() => teamsTaskFulfilled = true);
    });

    cronNotifications.start_task('0 8 * * *', () => {
        if (!boothsTaskFulfilled) {
            return;
        }

        boothsTaskFulfilled = false;

        UncollectedSWFeePaymentRunner.run(PAYMENT_FOR.BOOTHS, season)
            .catch(err => loggers.errors_log.error(err))
            .finally(() => boothsTaskFulfilled = true);
    });
}

function lostDisputeFeeFailedACHFeePayments () {
    let season = sails.config.sw_season.current;

    let ticketsTaskFulfilled = true;

    cronNotifications.start_task('30 6 * * *', () => {
        if(!ticketsTaskFulfilled) {
            return;
        }

        ticketsTaskFulfilled = false;

        AdditionalFeesPaymentRunner.run(PAYMENT_FOR.TICKETS, season)
            .catch(err => loggers.errors_log.error(err))
            .finally(() => ticketsTaskFulfilled = true);
    });

    let teamsTaskFulfilled = true;

    cronNotifications.start_task('30 7 * * *', () => {
        if(!teamsTaskFulfilled) {
            return;
        }

        teamsTaskFulfilled = false;

        AdditionalFeesPaymentRunner.run(PAYMENT_FOR.TEAMS, season)
            .catch(err => loggers.errors_log.error(err))
            .finally(() => teamsTaskFulfilled = true);
    });
}

function deletePendingPaymentSessions() {
    let isFulfilled = true;

    cronNotifications.start_task('* * * * *', () => {
        if(!isFulfilled) {
            return;
        }

        isFulfilled = false;

        DeletePendingPaymentSessionsRunner.run()
            .catch(err => loggers.errors_log.error(err))
            .finally(() => isFulfilled = true);
    });
}

function syncUSAVMemberFromSEAPI() {
    const season = sails.config.sw_season.current;
    let isFulfilledAthletes = true;

    cronNotifications.start_task('*/5 * * * *', () => {
        if(!isFulfilledAthletes) {
            return;
        }

        isFulfilledAthletes = false;

        SESyncProcess.processSync(SEUtils.MEMBER_TYPE.ATHLETE, season)
            .catch(err => loggers.errors_log.error(err))
            .finally(() => isFulfilledAthletes = true);
    });

    let isFulfilledStaffers = true;

    cronNotifications.start_task('3-59/10 * * * *', () => {
        if(!isFulfilledStaffers) {
            return;
        }

        isFulfilledStaffers = false;

        SESyncProcess.processSync(SEUtils.MEMBER_TYPE.STAFF, season)
            .catch(err => loggers.errors_log.error(err))
            .finally(() => isFulfilledStaffers = true);
    });
}

function __stopEmailSending () {
    let emailSendingIsInProgress = EmailMassSending.isSendingInProgress();

    if(emailSendingIsInProgress) {
        return EmailMassSending.stopEmailSending();
    } else {
        return Promise.resolve();
    }
}

let sigintListener = function () {
    return __stopEmailSending()
        .then(() => {
            process.exit(0);
        }).catch(err => {
            loggers.errors_log.error(err);
            process.exit(1);
        })
};

process.removeAllListeners('SIGINT', function () {});

process.on('SIGINT', sigintListener);
