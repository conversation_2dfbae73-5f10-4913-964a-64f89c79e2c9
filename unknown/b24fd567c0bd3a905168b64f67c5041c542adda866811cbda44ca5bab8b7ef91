const moment = require('moment');

class AAUUtilsService {
    get VALID_BACKGROUND_SCREENING_STATUS () {
        return 2;
    }

    get INVALID_BACKGROUND_SCREENING_STATUS () {
        return 1;
    }

    get MIN_AAU_AGE () {
        return 8;
    }

    get PROFILE_COMPLETED_FIELD () {
        return 'aau_profile_completed_at';
    }

    get AAU_FIELDS () {
        return {
            CLUB_CODE: 'club_code',
            MEMBERSHIP_IDENTIFIER: 'membership_identifier',
            ZIP_CODE: 'zip_code',
            LAST_NAME: 'last_name',
            BIRTH_DATE: 'birth_date',
        }
    }

    get AAU_IMPORT_FIELDS() {
        return {
            CATEGORY_CODE: 'CategoryCode',
            ADDRESS: 'AddressLine1',
            ADDRESS2: 'AddressLine2',
            CITY: 'City',
            COUNTRY: 'Country',
            STATE: 'State',
            ZIP_CODE: 'ZipCode',
            DISTRICT: 'AssociationCode',
            BIRTHDATE: 'DateOfBirth',
            EMAIL: 'EmailAddress',
            FIRST: 'FirstName',
            LAST: 'LastName',
            GENDER: 'Gender',
            GRAD_YEAR: 'GraduationYear',
            AAU_MEMBERSHIP_ID: 'MembershipID',
            AAU_MEMBERSHIP_ENDING_YEAR: 'MembershipTermEndingYear',
            PHONEM: 'Phone',
            CLUB_CODE: 'ClubCode',
        }
    }

    get MEMBER_TYPE () {
        return {
            ATHLETE: 'athlete',
            STAFF: 'staff',
        }
    }

    get CATEGORY_CODE () {
        return {
            A: 'athlete',
            N: 'staff',
        }
    }

    get GENDER () {
        return {
            M: 'male',
            F: 'female',
        }
    }

    get IMPORT_MODE () {
        return {
            DEFAULT: 'default',
            INSERT: 'insert'
        }
    }

    getMinAge (birthday) {
        if(!birthday) {
           return null;
        }

        const currentSeason = sails.config.sw_season.current;
        const bday = moment(birthday, 'YYYY-MM-DD');

        let AAUAge = currentSeason - bday.year();
        let seasonStart = moment().year(currentSeason).month(7 - 1).date(0);
        let birthdateInCurrentSeason = bday.clone().set('year', currentSeason);

        if (birthdateInCurrentSeason.isAfter(seasonStart)) {
            AAUAge--;
        }

        if (AAUAge < this.MIN_AAU_AGE) {
            AAUAge = this.MIN_AAU_AGE;
        }

        return AAUAge;
    }

    getSeasonEndByYear (year) {
        return moment().year(year).month('September').date(0).format('YYYY-MM-DD');
    }
}

module.exports = new AAUUtilsService();
