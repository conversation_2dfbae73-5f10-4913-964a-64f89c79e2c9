const knex = require('knex')({ client: 'pg' });
const argv = require('optimist').argv;

const QR_CONTENT_PREFIX = 'SWTm';
const QR_CODE_PATH = 'images/qrcode/';
const BASE_URL = sails.config.urls.home_page.baseUrl;
const DESCRIPTION_PAGE_LINK = `${BASE_URL}/online-checkin/`;
const IMAGE_LINK = `${BASE_URL}/${QR_CODE_PATH}`;
const CHECKIN_MODE = {
    PRIMARY_STAFF_BARCODES: 'primary_staff_barcodes',
    DEFAULT: 'default'
}

const _CHECKIN_EVENT_ACTION_TYPE = {
    CHECKED_IN: 'team.online-checkin.checkedin',
    ROSTER_LOCK: 'team.roster.lock.online-checkin',
};

const CHAPERONE_ROLE_ID = 15;

class OnlineCheckinTeam {

    constructor() {
        this.modifyStafferInfo = modifyStafferInfo.bind(this);
        this.notifyStaffers = notifyStaffers.bind(this);
    }

    get CHECKIN_MODE () {
        return CHECKIN_MODE;
    }

    async checkin({ eventID, teamsList, masterClubID, season, userID, staffers }) {
        let tr = null;

        try {
            const event = await getEventInfo(eventID);
            const checkinMode = event.checkin_mode;
            const eventOwnerId = event.event_owner_id;

            await verifyCheckinDeadLine(event);

            tr = await Db.begin();

            let stafferIds

            if (checkinMode === CHECKIN_MODE.PRIMARY_STAFF_BARCODES) {
                stafferIds = await primaryStaffBarcodeCheckin({
                    tr,
                    teamsList,
                    eventID,
                    masterClubID,
                    season,
                })
            } else {
                stafferIds = await defaultCheckin({
                    tr,
                    teamsList,
                    eventId: eventID,
                    masterClubId: masterClubID,
                    season,
                    staffers,
                });
            }

            await Promise.all(
                teamsList.map(rosterTeamID =>
                    updateRosterTeam({ tr, rosterTeamID, eventID })    
                ),
                teamsList.map(rosterTeamID =>
                    eventChangeLog({ tr, rosterTeamID, eventID, eventOwnerId, userID, action: _CHECKIN_EVENT_ACTION_TYPE.CHECKED_IN })
                ),
                teamsList.map(rosterTeamID =>
                    eventChangeLog({ tr, rosterTeamID, eventID, eventOwnerId, userID, action: _CHECKIN_EVENT_ACTION_TYPE.ROSTER_LOCK })
                ),
            );

            await tr.commit();

            await notifyStaffers({
                eventId: eventID,
                checkinMode,
                stafferIds,
            });

            await RosterSnapshotService.copyTeamsMembersValuesToRoster(teamsList, season);
        } catch (e) {
            if (tr && !tr.isCommited) {
                tr.rollback();
            }

            throw e;
        }
    }
    
    getMasterClubIDByRosterTeamID(teamID) {
        const query = knex('roster_team AS rt')
            .select('mt.master_club_id')
            .join('master_team AS mt', 'mt.master_team_id', 'rt.master_team_id')
            .where('rt.roster_team_id', teamID);

        return Db.query(query).then(({ rows: [row] }) => {
            return row && row.master_club_id;
        });
    }

    getStaffers(eventID, masterClubID, season) {
        const query = knex.raw(`
            SELECT 
                DISTINCT ON (ms.master_staff_id)
                FORMAT('%s %s', initcap(ms.first), initcap(ms.last)) "name",
                ms.master_staff_id "staff_id",
                regexp_replace(ms.phone, '[^0-9/s]', '', 'g') AS phone, ms.email
            FROM "roster_staff_role" rsr 
            INNER join "roster_team" rt 	
                ON rt.roster_team_id = rsr.roster_team_id 
                AND rt.event_id = ?
                AND rt.deleted IS NULL
            INNER JOIN "master_staff" ms 
                ON ms.master_staff_id = rsr.master_staff_id
                AND ms.master_club_id = ?
            JOIN master_staff_role msr 
                ON msr.master_staff_id = rsr.master_staff_id 
                AND msr.master_team_id = rsr.master_team_id
            JOIN event e 
                ON e.event_id = rt.event_id
            WHERE rsr.deleted IS NULL 
                AND 
                    (CASE 
                        WHEN (e.teams_settings->>'allow_chaperone_qr_code')::BOOLEAN IS NOT TRUE
                            THEN COALESCE(NULLIF(rsr.role_id, 0), msr.role_id) <> ?
                        ELSE TRUE
                    END)
              AND ms.season = ?
              AND rsr.deleted_by_user IS NULL
        `, [eventID, masterClubID, CHAPERONE_ROLE_ID, season]);

        return Db.query(query.toString()).then(({ rows }) =>  rows || []);
    }

    get CHAPERONE_ROLE_ID() {
        return CHAPERONE_ROLE_ID;
    }
}

/**
 * 
 * @typedef EventInfo
 * @property {boolean} over - True if online team checkin is over
 * @property {boolean} not_started - True if online team checkin not started
 * @property {boolean} available - True if online checkin is available on the event
 * @property {string} online_team_checkin_end - Date when online team checkin is end
 * @property {string} online_team_checkin_start - Date when online team checkin is start
 * @property {string} checkin_mode - Checkin mode
 * 
 */

/**
 * Get checkin mode with data for checkin verification 
 * 
 * @param {number} eventID 
 * 
 * @returns {Promise<EventInfo} - event info
 */
function getEventInfo(eventID) {
    const query = knex('event AS e')
        .select(
            { 
                over: knex.raw(`(
                    CASE 
                        WHEN 
                            (e.online_team_checkin_end IS NULL) OR 
                            (e.online_team_checkin_end < (NOW() AT TIME ZONE e.timezone))
                        THEN TRUE 
                        ELSE FALSE 
                    END 
                )`) 
            },
            {
                not_started: knex.raw(`(
                    CASE 
                        WHEN 
                            (e.online_team_checkin_start IS NULL) OR 
                            (e.online_team_checkin_start > (NOW() AT TIME ZONE e.timezone))
                        THEN TRUE 
                        ELSE FALSE 
                    END 
                )`) 
            },
            { 
                online_team_checkin_start: knex.raw(`
                    TO_CHAR(e.online_team_checkin_start, 'Mon DD, YYYY, HH12:MI AM')
                `) 
            },
            { 
                online_team_checkin_end: knex.raw(`
                    TO_CHAR(e.online_team_checkin_end, 'Mon DD, YYYY, HH12:MI AM')
                `) 
            },
            'e.online_team_checkin_available AS available',
            'e.online_team_checkin_mode AS checkin_mode',
            'e.event_owner_id'
        )
        .where('e.event_id', eventID);


    return Db.query(query).then(({ rows: [row] }) => row || {});
}

/**
 * @param {EventInfo} event
 * 
 * @throws Will throw an error if verification is fails
 */
function verifyCheckinDeadLine(event) {
    if (_.isEmpty(event)) {
        throw { validation: 'Event not found' };
    }

    if (!event.available) {
        throw {
            validation: 'Online Check In for this Event is not available'
        }
    }

    if (event.over) {
        const message = !event.online_team_checkin_end
            ? 'Online Check In Deadline passed!'
            : `Online Check In Deadline passed at ${event.online_team_checkin_end}`;

        throw { validation: message };
    }

    if (event.not_started) {
        const message = !event.online_team_checkin_start
            ? 'Online Check not started!'
            : `Online Check In will only be available from ${event.online_team_checkin_start}`;

        throw { validation: message };
    }
};

/**
 * @typedef PrimaryStaff
 * @property {number} staff_id - Master staff identifier
 * @property {string} email - Staffer's email
 * @property {string} phone - Staffer's phone
 * @property {string} name - Staffer's first + last
 * @property {number} staff_type - Staffer's type
 */

/**
 * 
 * @param {number} rosterTeamID 
 * @param {number} eventID
 * 
 * @returns {Promise<PrimaryStaff[]>}
 */

function getTeamPrimaryStaffers(rosterTeamID, eventID) {
    const query = knex('roster_team AS rt').distinct()
        .select(
            'ms.master_staff_id AS staff_id',
            'ms.email',
            'ms.phone',
            { staff_type: knex.raw('1') },
            { name: knex.raw(`FORMAT('%s %s', ms.first, ms.last)`) },
        )
        .leftJoin('roster_staff_role AS rsr', function() {
            this.on(knex.raw(`
                rsr.roster_team_id = rt.roster_team_id
                AND rsr.deleted IS NULL
                AND rsr.deleted_by_user IS NULL
            `))
        })
        .leftJoin('master_staff_role AS msr', function() {
            this.on(knex.raw(`
                msr.master_team_id = rsr.master_team_id
                AND msr.master_staff_id = rsr.master_staff_id
            `))
        })
        .join('master_staff AS ms', function() {
            this.on(knex.raw(`
                ms.master_staff_id = rsr.master_staff_id
                AND ms.deleted IS NULL
            `))
        })
        .join('event AS e', 'e.event_id', 'rt.event_id')
        .where('rt.roster_team_id', rosterTeamID)
        .where('rt.event_id', eventID)
        .whereRaw('COALESCE(rsr.primary, msr.primary) IS TRUE')
        .whereRaw('rt.deleted IS NULL')
        .whereRaw(`
            (CASE 
                WHEN (e.teams_settings->>'allow_chaperone_qr_code')::BOOLEAN IS NOT TRUE
                    THEN COALESCE(NULLIF(rsr.role_id, 0), msr.role_id) <> ?
                ELSE TRUE
            END)
        `, CHAPERONE_ROLE_ID);

    return Db.query(query).then(({ rows }) => rows || []);
}

/**
 * @param {Array<number>} staffersIDs 
 * 
 * @returns {boolean} - True if exists duplicates
 */
function staffersHasDuplicates(staffersIDs) {
    return new Set(staffersIDs).size !== staffersIDs.length;
}

async function saveWristbandPerson ({ tr, rosterTeamId, eventId, masterClubId, staffersList, staffersData }) {
	/*
	* 1. Remove all existing row for the event team
	* 2. Insert rows for passed staffers
	* 3. Return removed staffers
	*/

    let stafferType = {};

    staffersData.forEach(staff => {
        if (staffersList.indexOf(staff.staff_id) >= 0) {
            stafferType[staff.staff_id] = staff.staff_type;
        }
    });

	loggers.debug_log.verbose('Saving wristband person ...');

    const unassignedStaffers = await tr.query(
        `DELETE FROM "event_team_checkin" etc
        WHERE etc.event_id = $1 
        AND etc.roster_team_id = $2
        RETURNING etc.master_staff_id, 
            ( SELECT rt.master_team_id FROM "roster_team" rt WHERE rt.roster_team_id = $2 ) AS "master_team_id",
            ( SELECT rt.locked FROM "roster_team" rt WHERE rt.roster_team_id = $2 ) AS "locked"`,
        [eventId, rosterTeamId]
    );
    
    if (!_.isEmpty(unassignedStaffers.rows) && !unassignedStaffers.rows[0].locked) {
        await RosterSnapshotService.makeRosterSnapshot(unassignedStaffers.rows[0].master_team_id, rosterTeamId, eventId, sails.config.sw_season.current);
    }

    await staffersList.reduce((prev, stafferId) => {
        return prev
            .then(() =>
                tr.query(
                    `INSERT INTO "event_team_checkin" ("event_id", "roster_team_id", "master_staff_id", "staff_type")
                    SELECT $1, $2, ms.master_staff_id, $5
                    FROM "master_staff" ms 
                    WHERE ms.master_staff_id = $4
                    AND ms.master_club_id = $3`,
                    [
                        eventId,
                        rosterTeamId,
                        masterClubId,
                        stafferId,
                        stafferType[stafferId],
                    ]
                )
            )
    }, Promise.resolve());
}

function sendText (recepientPhone, checkinListURL) {
	loggers.debug_log.verbose('Sending letter ...');

    return SmsService.sendSms(checkinListURL, recepientPhone)
}

function sendLetter (templateData, recepient, template, subject) {
	return EmailService.renderAndSend({
        template	: template,
        data 		: templateData,
        from 		: 'SportWrench <<EMAIL>>', 
        to 			: recepient,
        subject 	: subject
    });
}

async function notifyStaffers({
    eventId,
    stafferIds,
    checkinMode,
}) {
    let templateType =
        checkinMode === CHECKIN_MODE.DEFAULT
            ? AEMService.CLUBS_GROUP_TYPE.DEFAULT_ONLINE_CHECKIN_TYPE
            : AEMService.CLUBS_GROUP_TYPE.PRIMARY_STAFF_ONLINE_CHECKIN_TYPE;

    return stafferIds.reduce(async (prevPromise, staffId) => {
        await prevPromise;

        const receiverFilters = {
            master_staff_id: staffId,
            template_type: templateType
        };

        return AEMSenderService.sendTriggerNotification(
            AEMService.CLUB_GROUP,
            templateType,
            eventId,
            receiverFilters
        ).catch((err) => {
            loggers.errors_log.error(
                'Notification error for ' +
                    JSON.stringify(receiverFilters, null, 2),
                err
            );
            throw err;
        });
    }, Promise.resolve());
}

async function modifyStafferInfo ({ tr, staffData, masterClubId, eventId, season }) {
    // update staffer data
    // create barcode if needed
    // send notifications
    
    const checkinBarcode = await OnlineCheckinService.common.generateCheckinBarcode();
    const updatedStaff = await tr.query(
        `UPDATE "master_staff" ms 
         SET "phone"             = $3::TEXT,
             "email"             = $4::TEXT,
             "checkin_barcode"  = COALESCE("checkin_barcode"::TEXT, $5::TEXT)
         WHERE ms."master_staff_id" = $1
            AND ms.master_club_id = $2
            AND ms.deleted IS NULL 
            AND ms.season = $6
         RETURNING ms."checkin_barcode", ms.phone`, 
        [
            staffData.staff_id,
            masterClubId,
            (staffData.phone || null),
            (staffData.email || null),
            checkinBarcode,
            season
        ]
    ).then(result => result && result.rows[0]);
    if(_.isEmpty(updatedStaff)) {
        throw { validation: 'Staff not found' };
    }

    const realBarcode = updatedStaff.checkin_barcode;
    const barcodeCreated = (checkinBarcode === realBarcode);
    const imageName = `${QR_CONTENT_PREFIX}${realBarcode}`;

    if(barcodeCreated) {
        await OnlineCheckinService.common.generateQRCode(imageName);
    }
    const credentials = {
        sendText        : barcodeCreated,
        descriptionLink : `${DESCRIPTION_PAGE_LINK}${realBarcode}/event/${eventId}`,
        phone           :updatedStaff.phone,
    };
    loggers.debug_log.verbose(credentials);

    if (credentials.phone && credentials.sendText && argv.prod) {
        await sendText(
            credentials.phone,
            credentials.descriptionLink
        ).catch((err) => {
            loggers.errors_log.error('Send text error', err);
        })
    }

    return credentials;
}

/**
 * @param {Object} obj 
 * @param {number} obj.eventID
 * @param {number} obj.rosterTeamID
 * @param {number} obj.masterClubID
 */
async function checkinTeam({ tr, rosterTeamID, eventID, masterClubID, season }) {
    const staffers = await getTeamPrimaryStaffers(rosterTeamID, eventID);
    const staffersIdentifiers = staffers.map(staff => Number(staff.staff_id));

    if (staffersHasDuplicates(staffersIdentifiers)) {
        throw { validation: 'Duplicate Staff passed!' };
    }

    await saveWristbandPerson({
        tr,
        rosterTeamId: rosterTeamID,
        eventId: eventID,
        masterClubId: masterClubID,
        staffersList: staffersIdentifiers,
        staffersData: staffers,
    });

    await Promise.all(
        staffers.map(staff => modifyStafferInfo({
            tr,
            staffData: staff,
            masterClubId: masterClubID,
            eventId: eventID,
            season,
        }))
    );

    return staffersIdentifiers
}

async function primaryStaffBarcodeCheckin({ tr, teamsList, eventID, masterClubID, season }) {
    const staffers = await Promise.all(
        teamsList.map(rosterTeamID => 
            checkinTeam({
                tr,
                rosterTeamID,
                eventID,
                masterClubID,
                season,
            })
        )
    )
    return _.flatten(staffers)
};

async function defaultCheckin({ tr, teamsList, eventId, masterClubId, season, staffers}) {
    if(!_.isArray(staffers)) {
        throw { validation: 'Expecting "staffers" to be an array' };
    }

    if(_.isEmpty(staffers)) {
        throw { validation: 'No Staffers choosen' } ;
    }

    const staffersIdentifiers = staffers.map(staff => Number(staff.staff_id));


    if(staffersIdentifiers.length !== staffers.length) {
        throw { validation: 'Invalid staffers passed' };
    }

    if(staffersHasDuplicates(staffersIdentifiers)) {
        throw { validation: 'Duplicate Staff passed!' } ;
    }

    await Promise.all(
        teamsList.map(function (rosterTeamId) {
            return saveWristbandPerson({ tr, rosterTeamId, eventId, masterClubId, staffersList: staffersIdentifiers, staffersData: staffers });
        })
        // assignmentResults - list of removed staff assignments
    );

    await Promise.all(
        staffers.map(function (staff) {
            return modifyStafferInfo({ 
                tr, 
                staffData: staff, 
                masterClubId, 
                eventId, 
                season,
            })
        })
    );

    return staffersIdentifiers
};

function updateRosterTeam({ tr, rosterTeamID, eventID }) {
    const query = knex('roster_team').update({
        locked: true,
        online_checkin_date: knex.raw('NOW()')
    })
    .where('roster_team_id', rosterTeamID)
    .where('event_id', eventID);

    return tr.query(query);
}

function eventChangeLog({ tr, eventID, eventOwnerId, rosterTeamID, userID, action }) {
    const query = knex('event_change')
        .insert({
            'event_id': eventID,
            'event_owner_id': eventOwnerId,
            'roster_team_id': rosterTeamID,
            'user_id': userID,
            action,
        });

    return tr.query(query);
}

module.exports = new OnlineCheckinTeam();
