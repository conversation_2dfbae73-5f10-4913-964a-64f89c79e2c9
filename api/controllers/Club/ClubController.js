'use strict';

const csvUtils = require('../../lib/csvUtils');
const {xmlFileTypeUploadValidation} = require("../../lib/FileStreamUploadService");
const season = sails.config.sw_season.current;

module.exports = {
    // GET /api/club
    find: function (req, res) {
        let $master_club_id = parseInt(req.session.passport.user.master_club_id, 10),
            $club_owner_id  = parseInt(req.session.passport.user.club_owner_id, 10);

        if(!$club_owner_id)
            return res.validation('Not a club owner');

        let params = [$club_owner_id];
        let query =
            `SELECT    
                mc.club_name, mc.code, mc.region, mc.aau_primary_membership_id,
                mc.aau_club_code, mc.aau_primary_zip, mc.address, mc.city,    
                mc.state, mc.zip, mc.sport_id, mc.country, mc.team_prefix,     
                mc.has_male_teams, mc.has_female_teams, mc.has_coed_teams,   
                mc.director_first, mc.director_last, mc.master_club_id,   
                (mc.director_first || ' ' || mc.director_last) director_name,  
                mc.director_email, mc.administrative_email, s.name AS sport,  
                mc.director_phone, mc.director_gender, mc.director_usav_code,  
                mc.profile_completed_at IS NULL "not_completed",  
                to_char(mc.director_birthdate, 'YYYY-MM-DD') director_birthdate, (  
                    SELECT ROW_TO_JSON(sp_sanc) 
                    FROM ( 
                        SELECT  
                            STRING_AGG(ss.name, ', ') "str",  
                            COALESCE(ARRAY_AGG(ss.sport_sanctioning_id), '{ }') "arr" 
                        FROM master_club_sanctioning msc     
                        INNER JOIN sport_sanctioning ss   
                            ON ss.sport_sanctioning_id = msc.sport_sanctioning_id   
                        WHERE msc.master_club_id = mc.master_club_id  
                    ) sp_sanc 
                ) sanctionings, (  
                    SELECT ROW_TO_JSON(sp_var) 
                    FROM ( 
                        SELECT  
                            STRING_AGG(sv.name, ', ') "str",  
                            ARRAY_AGG(sv.sport_variation_id) "arr" 
                        FROM master_club_sport_variation mcsv   
                        INNER JOIN sport_variation sv   
                            ON mcsv.sport_variation_id = sv.sport_variation_id   
                        WHERE mcsv.master_club_id = mc.master_club_id  
                    ) sp_var 
                ) variations,
                (${XlsxMemberService.import.isXLSXImportAllowedQuery()}) "allow_xlsx_import",
                TO_CHAR(mc.modified, 'YYYY-MM-DD hh:mm:ss') AS modified
            FROM master_club mc    
            LEFT JOIN sport s    
                ON s.sport_id = mc.sport_id    
            INNER JOIN club_owner co   
                ON co.club_owner_id = mc.club_owner_id   
            WHERE mc.club_owner_id = $1`;

        if($master_club_id) {
            params.push($master_club_id);
            query += ' AND mc.master_club_id = $2';
        }

        Db.query(query, params)
        .then(result => {
            let club = _.first(result.rows);

            if(_.isEmpty(club)) {
                res.status(200).json({ club: {} });
            } else {
                // hack to integrate to the old UI 29.09.2015
                // TODO: refactor this and Club Edit Form
                club.sport_sanctionings         = club.sanctionings && club.sanctionings.arr;
                club.sport_sanctionings_str     = club.sanctionings && club.sanctionings.str;
                club.sanctionings               = undefined;
                club.sport_variations           = club.variations && club.variations.arr;
                club.sport_variations_str       = club.variations && club.variations.str;
                club.variations                 = undefined;

                res.status(200).json({ club });
            }
        }).catch(err => {
            res.customRespError(err);
        });
    },
    // POST /api/club/roster-import
    foreignRosterImport: async function (req, res) {
        const $masterClubId = parseInt(req.session.passport.user.master_club_id, 10),
            $clubOwnerId  = parseInt(req.session.passport.user.club_owner_id, 10),
            fileStream = req.file('roster-list')._files[0].stream;

        try {
            xmlFileTypeUploadValidation(fileStream);

            await XlsxMemberService.import.run($masterClubId, $clubOwnerId, fileStream, season);

            await RosterSnapshotService.clubSnapshot($masterClubId, season);

            res.ok();
        } catch (err) {
            res.customRespError(err);
        }
    },
    // PUT /api/club
    update:  async function (req, res) {
        try {
            LogRequestService.logClubUpdating(req);

            if(!req.user.role_club_director) {
                return res.forbidden('You have no Club Director Role');
            }

            let masterClubId = req.session.passport.user.master_club_id,
                clubOwnerId  = req.session.passport.user.club_owner_id;

            const updatedData = await ClubService.updateMasterClub(req.body, masterClubId, clubOwnerId);

            const changedData = ClubService.getChangedLocationData(updatedData);

            if (!_.isEmpty(changedData)) {
                let events = await ClubService.events.getUpcomingEvents(masterClubId);

                events = ClubService.events.generateLinksToTeamsList(events);

                await sendEmailOnLocationChange(changedData, updatedData.club_name, events)
                    .catch(err => loggers.errors_log.error(err))
            }

            res.status(200).json({});
        } catch (error) {
            if (error.notValidFields && error.notValidFields.length) {
                return res.status(400).json({
                    validation: error.validation,
                    not_valid_fields: error.notValidFields,
                })
            }

            res.customRespError(error);
        }
    },
    // POST /master_club
    create: function (req, res) {
        LogRequestService.logClubCreation(req);

        if(!req.user.role_club_director) {
            return res.forbidden('You have no Club Director Role');
        }

        if(!!req.user.master_club_id) {
            return res.validation('Club Already exists');
        }

        let season = sails.config.sw_season.current;
        let userID = req.user.user_id;

        ClubService.createMasterClub(req.body, season, userID)
        .then(data => saveIDsToSession(req, data))
        .then(() => {
            res.ok();
        })
        .catch((error) => {
            if (error.notValidFields && error.notValidFields.length) {
                return res.status(400).json({
                    validation: error.validation,
                    not_valid_fields: error.notValidFields,
                })
            }

            res.customRespError(error);
        });

        function saveIDsToSession (req, data) {
            return new Promise((resolve, reject) => {
                req.session.passport.user.master_club_id = data.master_club_id;
                req.session.passport.user.club_owner_id  = data.club_owner_id;

                req.session.save(err => {
                    if (err) {
                        reject(err);
                    } else {
                        resolve();
                    }
                });
            })
        }
    },
    // POST /api/club/import/:provider(webpoint|sportengine|aau)
    import: async function(req, res) {
        let master_club_id = req.session.passport.user.master_club_id,
            club_owner_id = req.session.passport.user.club_owner_id,
            provider = req.params.provider,
            data = _.clone(req.body);

        if (_.isEmpty(data)) {
            return res.validation('Empty body passed');
        }

        try {
            if (provider === 'sportengine') {
                await SportEngineMemberService.import.create(master_club_id, club_owner_id, data);
            } else if (provider === 'aau') {
                await AauMemberService.import.create(master_club_id, club_owner_id, data);
            } else {
                throw new Error('Unknown provider');
            }

            res.ok();
        } catch (err) {
            res.customRespError(err);
        }
    },

    // GET /api/club/sportengine/import
    sportEngineImportData: async function (req, res) {
        let masterClubID = req.session.passport.user.master_club_id,
            clubOwnerID  = req.session.passport.user.club_owner_id;

        Promise.all([
            SportEngineMemberService.import.queue.getClubImport(masterClubID),
            SportEngineMemberService.import.getUSAVClubData(masterClubID, clubOwnerID),
            RosterSnapshotService.findBlockedEvents(masterClubID)
        ]).then(([queueData, clubUSAVData, blockedEventsList]) => {
            res.status(200).json({
                queue: queueData,
                club: clubUSAVData,
                blockedEventsList
            })
        }).catch(err => res.customRespError(err));
    },

    // GET /api/club/aau/import
    aauImportData: async function (req, res) {
        let masterClubID = req.session.passport.user.master_club_id,
            clubOwnerID  = req.session.passport.user.club_owner_id;

        Promise.all([
            AauMemberService.import.queue.getClubImport(masterClubID),
            AauMemberService.import.getAAUClubData(masterClubID, clubOwnerID),
            RosterSnapshotService.findBlockedEvents(masterClubID)
        ]).then(([queueData, clubAAUData, blockedEventsList]) => {
            res.status(200).json({
                queue: queueData,
                club: clubAAUData,
                blockedEventsList
            })
        }).catch(err => res.customRespError(err));
    },

    // GET /api/club/webpoint/import
    wp_data: function (req, res) {
        let master_club_id = req.session.passport.user.master_club_id,
            club_owner_id  = req.session.passport.user.club_owner_id;

        Promise.all([
            Db.query(
                `SELECT
                     mc.webpoint_import_agree "agree", 
                     mc.webpoint_username "username"
                 FROM master_club mc 
                 WHERE mc.master_club_id = $1
                    AND mc.club_owner_id = $2`,
                [master_club_id, club_owner_id]
            ),
            Db.query(
                `SELECT 
                    wq.created, TO_CHAR(wq.requested, 'DD/MM/YYYY HH12:MI AM') "requested" 
                 FROM webpoint_queue wq 
                 WHERE wq.responded IS NULL  
                    AND wq.requested IS NOT NULL 
                    AND wq.master_club_id = $1 
                 ORDER BY wq.requested DESC 
                 LIMIT 1`,
                [master_club_id]
            ),
            RosterSnapshotService.findBlockedEvents(master_club_id)
        ]).then(result => {
            let webpoint_data  = result[0].rows[0] || {},
                queue_data     = result[1].rows[0] || {},
                blocked_events = result[2],
                respData;

            if(!_.isEmpty(queue_data) && !webpoint_data.agree) {
                respData = { webpoint_data, blocked_events };
            } else {
                respData = { webpoint_data, queue_data, blocked_events };
            }

            res.status(200).json(respData);
        }).catch(err => {
            res.customRespError(err);
        });
    },

    // GET /api/club/teams/result-report
    resultReport: async function (req, res) {
        const masterClubId     = req.session.passport.user.master_club_id,
              eventIds = (req.query.eventIds || '').split(','),
              teamId  = req.query.teamId;

        if(!eventIds || !eventIds.length) return res.validation('No event ids passed');
        if(!teamId) return res.validation('No team id passed');

        try {
            const [rowData, team] = await Promise.all([
                ClubService.report.getResultReport(masterClubId, eventIds, teamId),
                ClubService.teams.getTeamById(teamId),
            ]);
            if (!team) {
                return res.notFound();
            }
            const filename = `Result_Report_${team.team_name}.csv`;
            res.setHeader('Content-Type', 'text/csv');
            res.setHeader(
                'Content-Disposition',
                `attachment; filename=${filename}`);
            res.write('Opposing Team Name,Opposing Team Code,Event Name,Event Date,Outcome,Score 1,Score 2,Score 3,Score 4,Score 5\n');
            rowData.forEach(row => {
                for (const match of row.match_results) {
                    const opponentName = csvUtils.formatTextForCsv(match['Opponent name']);
                    const opponentCode = csvUtils.formatTextForCsv(match['Opponent code']);
                    const eventName = csvUtils.formatTextForCsv(match['event_name']);
                    const eventDateStart = match['even_date_start'];
                    const outcome = csvUtils.formatTextForCsv(match['Result']);

                    const set1 = ClubService.report.normalizeSets(match.winner, match.match_team, match.set1);
                    const set2 = ClubService.report.normalizeSets(match.winner, match.match_team, match.set2);
                    const set3 = ClubService.report.normalizeSets(match.winner, match.match_team, match.set3);
                    const set4 = ClubService.report.normalizeSets(match.winner, match.match_team, match.set4);
                    const set5 = ClubService.report.normalizeSets(match.winner, match.match_team, match.set5);

                    res.write(`${opponentName},${opponentCode},${eventName},${eventDateStart},${outcome},${set1},${set2},${set3},${set4},${set5}\n`);
                }
            });
            res.end();
        } catch (err) {
            res.customRespError(err);
        }
    },
}

function sendEmailOnLocationChange (changedData, clubName, events) {
    let subject = `${clubName} location changes`;

    let data = { baseUrl: sails.config.urls.home_page.baseUrl, changedData, events };

    return EmailService.renderAndSend({
        template    : 'club/location_change',
        layout      : 'official/layout', /* Temporary hack */
        from        : UserService.reg.SW_ACC,
        to          : '"Eugene Tichenor" <<EMAIL>>',
        cc          : '"sw debug" <<EMAIL>>',
        subject     : subject,
        data        : data
    });
}
