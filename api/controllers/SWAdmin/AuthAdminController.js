module.exports = {
    //POST /api/admin/signin
    signIn (req, res) {
        return AdminUserService.auth.signIn(req, res)
            .then(user => res.status(200).json({user}))
            .catch(res.customRespError.bind(res))
    },

    //POST /api/admin/signout
    signOut (req, res) {
        return AdminUserService.auth.signOut(req, res)
            .then(() => res.ok())
            .catch(res.customRespError.bind(res))
    }
}
